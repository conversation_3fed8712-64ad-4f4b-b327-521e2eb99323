# This is the CMakeCache file.
# For build in directory: /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

//Path to a program.
CCACHE:FILEPATH=CCACHE-NOTFOUND

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Path to a program.
CMAKE_FORMAT_PROGRAM:FILEPATH=CMAKE_FORMAT_PROGRAM-NOTFOUND

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=NS3

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CONVERT:FILEPATH=/usr/bin/convert

//Path to a program.
DIA:FILEPATH=/usr/bin/dia

//Path to a program.
DOT:FILEPATH=/usr/bin/dot

//Path to a program.
DOXYGEN:FILEPATH=/usr/bin/doxygen

//Dot tool for use with Doxygen
DOXYGEN_DOT_EXECUTABLE:FILEPATH=/usr/bin/dot

//Doxygen documentation generation tool (http://www.doxygen.org)
DOXYGEN_EXECUTABLE:FILEPATH=/usr/bin/doxygen

//Path to a program.
DVIPNG:FILEPATH=/usr/bin/dvipng

//Path to a program.
EPSTOPDF:FILEPATH=/usr/bin/epstopdf

//Path to a file.
FREETYPE_INCLUDE_DIR_freetype2:PATH=/usr/include/freetype2

//Path to a file.
FREETYPE_INCLUDE_DIR_ft2build:PATH=/usr/include/freetype2

//Path to a library.
FREETYPE_LIBRARY_DEBUG:FILEPATH=FREETYPE_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FREETYPE_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libfreetype.so

//Path to a library.
GSL_CBLAS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a library.
GSL_CBLAS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a file.
GSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

//Path to a library.
GSL_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

GSL_ROOT_DIR:STRING=/usr

//Path to a file.
GTK3_ATK_INCLUDE_DIR:PATH=/usr/include/atk-1.0

//Path to a library.
GTK3_ATK_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libatk-1.0.so

//Path to a file.
GTK3_CAIRO_INCLUDE_DIR:PATH=/usr/include/cairo

//Path to a library.
GTK3_CAIRO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libcairo.so

//Path to a file.
GTK3_FONTCONFIG_INCLUDE_DIR:PATH=/usr/include

//Path to a file.
GTK3_GDKCONFIG_INCLUDE_DIR:PATH=/usr/include/gtk-3.0

//Path to a file.
GTK3_GDK_INCLUDE_DIR:PATH=/usr/include/gtk-3.0

//Path to a library.
GTK3_GDK_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgdk-3.so

//Path to a file.
GTK3_GDK_PIXBUF_INCLUDE_DIR:PATH=/usr/include/gdk-pixbuf-2.0

//Path to a library.
GTK3_GDK_PIXBUF_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so

//Path to a file.
GTK3_GIO_INCLUDE_DIR:PATH=/usr/include/glib-2.0

//Path to a library.
GTK3_GIO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgio-2.0.so

//Path to a file.
GTK3_GLIBCONFIG_INCLUDE_DIR:PATH=/usr/lib/x86_64-linux-gnu/glib-2.0/include

//Path to a file.
GTK3_GLIB_INCLUDE_DIR:PATH=/usr/include/glib-2.0

//Path to a library.
GTK3_GLIB_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libglib-2.0.so

//Path to a file.
GTK3_GOBJECT_INCLUDE_DIR:PATH=/usr/include/glib-2.0

//Path to a library.
GTK3_GOBJECT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgobject-2.0.so

//Path to a library.
GTK3_GTHREAD_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgthread-2.0.so

//Path to a file.
GTK3_GTK_INCLUDE_DIR:PATH=/usr/include/gtk-3.0

//Path to a library.
GTK3_GTK_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgtk-3.so

//Path to a file.
GTK3_HARFBUZZ_INCLUDE_DIR:PATH=/usr/include/harfbuzz

//Path to a file.
GTK3_PANGO_INCLUDE_DIR:PATH=/usr/include/pango-1.0

//Path to a library.
GTK3_PANGO_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libpango-1.0.so

//Path to a file.
HarfBuzz_INCLUDE_DIR:PATH=/usr/include/harfbuzz

//Path to a library.
HarfBuzz_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libharfbuzz.so

//Path to a program.
LATEXMK:FILEPATH=/usr/bin/latexmk

//Path to a file.
LIBXML2_INCLUDE_DIR:PATH=/usr/include/libxml2

//Path to a library.
LIBXML2_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libxml2.so

//Path to a program.
LIBXML2_XMLLINT_EXECUTABLE:FILEPATH=/usr/bin/xmllint

//Path to a program.
LLD:FILEPATH=LLD-NOTFOUND

//Path to a program.
MAKE:FILEPATH=/usr/bin/make

//Path to a program.
MOLD:FILEPATH=MOLD-NOTFOUND

//Enable assert on failure
NS3_ASSERT:BOOL=ON

//Value Computed by CMake
NS3_BINARY_DIR:STATIC=/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache

//Enforce cody style with clang-format
NS3_CLANG_FORMAT:BOOL=OFF

//Use clang-tidy static analysis
NS3_CLANG_TIDY:BOOL=OFF

//Collect compilation statistics to analyze the build process
NS3_CLANG_TIMETRACE:BOOL=OFF

//Colorize CMake messages
NS3_COLORED_OUTPUT:BOOL=OFF

//Enable code coverage measurements and report generation
NS3_COVERAGE:BOOL=OFF

//Zero lcov counters before running. Requires NS3_COVERAGE=ON
NS3_COVERAGE_ZERO_COUNTERS:BOOL=OFF

//Enable DES Metrics event collection
NS3_DES_METRICS:BOOL=OFF

//List of modules to disable (e.g. lte;wimax;wave)
NS3_DISABLED_MODULES:STRING=

//Enable fd-net-device DPDK features
NS3_DPDK:BOOL=OFF

//Build with emulation support
NS3_EMU:BOOL=ON

//List of modules to enable (e.g. core;network;internet)
NS3_ENABLED_MODULES:STRING=

//Embed version info into libraries
NS3_ENABLE_BUILD_VERSION:BOOL=OFF

//Set executables ownership to root and enable the SUID flag
NS3_ENABLE_SUDO:BOOL=OFF

//Enable examples to be built
NS3_EXAMPLES:BOOL=ON

//List of modules that should have their examples and tests built
// (e.g. lte;wifi)
NS3_FILTER_MODULE_EXAMPLES_AND_TESTS:STRING=

//Build with GSL support
NS3_GSL:BOOL=ON

//Build with GTK3 support
NS3_GTK3:BOOL=ON

//Use IWYU to determine unnecessary headers included
NS3_INCLUDE_WHAT_YOU_USE:BOOL=OFF

//Int64x64 implementation
NS3_INT64X64:STRING=INT128

//Value Computed by CMake
NS3_IS_TOP_LEVEL:STATIC=ON

//Build with link-time optimization
NS3_LINK_TIME_OPTIMIZATION:BOOL=OFF

//Use LWYU to determine unnecessary linked libraries
NS3_LINK_WHAT_YOU_USE:BOOL=OFF

//Enable logging to be built
NS3_LOG:BOOL=ON

//Build a single shared ns-3 library and link it against executables
NS3_MONOLIB:BOOL=OFF

//Build with MPI support
NS3_MPI:BOOL=OFF

//Build with -march=native -mtune=native
NS3_NATIVE_OPTIMIZATIONS:BOOL=OFF

//Build netanim
NS3_NETANIM:BOOL=OFF

//Directory to store built artifacts
NS3_OUTPUT_DIRECTORY:STRING=

//Precompile module headers to speed up compilation
NS3_PRECOMPILE_HEADERS:BOOL=ON

//Build ns-3 python bindings
NS3_PYTHON_BINDINGS:BOOL=OFF

//Export all third-party libraries
//\nand include directories to ns-3 module consumers
NS3_REEXPORT_THIRD_PARTY_LIBRARIES:BOOL=ON

//Build with address, leak and undefined sanitizers
NS3_SANITIZE:BOOL=OFF

//Build with memory sanitizer
NS3_SANITIZE_MEMORY:BOOL=OFF

//Value Computed by CMake
NS3_SOURCE_DIR:STATIC=/home/<USER>/ns3/ns-allinone-3.37/ns-3.37

//Build with SQLite support
NS3_SQLITE:BOOL=ON

//Build a static ns-3 library and link it against executables
NS3_STATIC:BOOL=OFF

//Build with Tap support
NS3_TAP:BOOL=ON

//Enable tests to be built
NS3_TESTS:BOOL=ON

//Print additional build system messages
NS3_VERBOSE:BOOL=OFF

//Build visualizer module
NS3_VISUALIZER:BOOL=ON

//Enable compiler warnings
NS3_WARNINGS:BOOL=ON

//Treat warnings as errors. Requires NS3_WARNINGS=ON
NS3_WARNINGS_AS_ERRORS:BOOL=ON

//Build with brite support
NS3_WITH_BRITE:PATH=

//Build with click support
NS3_WITH_CLICK:PATH=

//Build with Openflow support
NS3_WITH_OPENFLOW:PATH=

//Path to a program.
PDFLATEX:FILEPATH=/usr/bin/pdflatex

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
PYTHON3:FILEPATH=/usr/bin/python3

//Path to a program.
ProcessorCount_cmd_nproc:FILEPATH=/usr/bin/nproc

//Path to a program.
ProcessorCount_cmd_sysctl:FILEPATH=/usr/sbin/sysctl

//Path to sphinx-build executable
SPHINX_EXECUTABLE:FILEPATH=/usr/bin/sphinx-build

//Output standalone HTML files
SPHINX_OUTPUT_HTML:BOOL=ON

//Output man pages
SPHINX_OUTPUT_MAN:BOOL=ON

//When building documentation treat warnings as errors
SPHINX_WARNINGS_AS_ERRORS:BOOL=ON

//Path to a file.
SQLite3_header_internal_sqlite3.h:FILEPATH=/usr/include/sqlite3.h

//Path to a library.
SQLite3_library_internal_sqlite3:FILEPATH=/usr/lib/x86_64-linux-gnu/libsqlite3.so

//Path to a program.
VALGRIND:FILEPATH=/usr/bin/valgrind

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.74.0

//Path to a file.
brite_header_internal_Brite.h:FILEPATH=brite_header_internal_Brite.h-NOTFOUND

//Path to a library.
brite_library_internal_brite:FILEPATH=brite_library_internal_brite-NOTFOUND

//Path to a file.
click_header_internal_simclick.h:FILEPATH=click_header_internal_simclick.h-NOTFOUND

//Path to a library.
click_library_internal_click:FILEPATH=click_library_internal_click-NOTFOUND

//Path to a library.
click_library_internal_nsclick:FILEPATH=click_library_internal_nsclick-NOTFOUND

//Dependencies for the target
libantenna-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libantenna;general;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libantenna_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libaodv-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libaodv;general;libinternet;libwifi;general;-Wl,--as-needed;

//Dependencies for the target
libaodv_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libwifi;general;-Wl,--as-needed;

//Dependencies for the target
libapplications-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libapplications;general;libinternet;libstats;general;-Wl,--as-needed;

//Dependencies for the target
libapplications_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libstats;general;-Wl,--as-needed;

//Dependencies for the target
libbridge_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libbuildings-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libbuildings;general;libmobility;libpropagation;general;-Wl,--as-needed;

//Dependencies for the target
libbuildings_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libmobility;general;libpropagation;general;-Wl,--as-needed;

//Dependencies for the target
libconfig-store_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;libnetwork;general;/usr/lib/x86_64-linux-gnu/libxml2.so;general;/usr/lib/x86_64-linux-gnu/libfreetype.so;general;/usr/lib/x86_64-linux-gnu/libglib-2.0.so;general;/usr/lib/x86_64-linux-gnu/libgthread-2.0.so;general;/usr/lib/x86_64-linux-gnu/libgobject-2.0.so;general;/usr/lib/x86_64-linux-gnu/libgio-2.0.so;general;/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so;general;/usr/lib/x86_64-linux-gnu/libgdk-3.so;general;/usr/lib/x86_64-linux-gnu/libgtk-3.so;general;/usr/lib/x86_64-linux-gnu/libcairo.so;general;/usr/lib/x86_64-linux-gnu/libpango-1.0.so;general;/usr/lib/x86_64-linux-gnu/libatk-1.0.so;general;-Wl,--as-needed;

//Dependencies for the target
libcore-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;/usr/lib/x86_64-linux-gnu/libgsl.so;/usr/lib/x86_64-linux-gnu/libgslcblas.so;general;-Wl,--as-needed;

//Dependencies for the target
libcore_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;/usr/lib/x86_64-linux-gnu/libgsl.so;general;/usr/lib/x86_64-linux-gnu/libgslcblas.so;general;-Wl,--as-needed;

//Dependencies for the target
libcsma-layout_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libinternet;general;libcsma;general;libpoint-to-point;general;-Wl,--as-needed;

//Dependencies for the target
libcsma_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libdsdv-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libdsdv;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libdsdv_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libdsr-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libdsr;general;libinternet;libwifi;general;-Wl,--as-needed;

//Dependencies for the target
libdsr_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libwifi;general;-Wl,--as-needed;

//Dependencies for the target
libenergy-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libenergy;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libenergy_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libfd-net-device_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libflow-monitor_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libstats;general;-Wl,--as-needed;

//Dependencies for the target
libinternet-apps-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet-apps;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libinternet-apps_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libinternet-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libnetwork;libcore;libbridge;libtraffic-control;general;-Wl,--as-needed;

//Dependencies for the target
libinternet_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libcore;general;libbridge;general;libtraffic-control;general;-Wl,--as-needed;

//Dependencies for the target
liblr-wpan-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;liblr-wpan;general;libnetwork;libcore;libmobility;libspectrum;libpropagation;general;-Wl,--as-needed;

//Dependencies for the target
liblr-wpan_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libcore;general;libmobility;general;libspectrum;general;libpropagation;general;-Wl,--as-needed;

//Dependencies for the target
liblte-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;liblte;general;libfd-net-device;libcore;libnetwork;libspectrum;libstats;libbuildings;libvirtual-net-device;libpoint-to-point;libapplications;libinternet;libcsma;libconfig-store;general;-Wl,--as-needed;

//Dependencies for the target
liblte_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libfd-net-device;general;libcore;general;libnetwork;general;libspectrum;general;libstats;general;libbuildings;general;libvirtual-net-device;general;libpoint-to-point;general;libapplications;general;libinternet;general;libcsma;general;libconfig-store;general;-Wl,--as-needed;

//Dependencies for the target
libmesh-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libmesh;general;libinternet;libwifi;libapplications;general;-Wl,--as-needed;

//Dependencies for the target
libmesh_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libwifi;general;libapplications;general;-Wl,--as-needed;

//Dependencies for the target
libmobility-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libmobility;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libmobility_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libnetanim-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetanim;general;libinternet;libmobility;libwimax;libwifi;libcsma;liblte;libuan;libenergy;liblr-wpan;libwave;libpoint-to-point-layout;general;-Wl,--as-needed;

//Dependencies for the target
libnetanim_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libmobility;general;libwimax;general;libwifi;general;libcsma;general;liblte;general;libuan;general;libenergy;general;liblr-wpan;general;libwave;general;libpoint-to-point-layout;general;-Wl,--as-needed;

//Dependencies for the target
libnetwork-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libcore;libstats;general;-Wl,--as-needed;

//Dependencies for the target
libnetwork_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;libstats;general;-Wl,--as-needed;

//Dependencies for the target
libnix-vector-routing-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnix-vector-routing;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libnix-vector-routing_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libolsr-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libolsr;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libolsr_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libpoint-to-point-layout_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libpoint-to-point;general;libmobility;general;-Wl,--as-needed;

//Dependencies for the target
libpoint-to-point-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libpoint-to-point;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libpoint-to-point_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libpropagation-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libpropagation;general;libnetwork;libmobility;general;-Wl,--as-needed;

//Dependencies for the target
libpropagation_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libmobility;general;-Wl,--as-needed;

//Dependencies for the target
libsixlowpan-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libsixlowpan;general;libinternet;libinternet;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libsixlowpan_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libinternet;general;libinternet;general;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libspectrum-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libspectrum;general;libpropagation;libantenna;general;-Wl,--as-needed;

//Dependencies for the target
libspectrum_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libpropagation;general;libantenna;general;-Wl,--as-needed;

//Dependencies for the target
libstats-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libstats;general;libcore;/usr/lib/x86_64-linux-gnu/libsqlite3.so;general;-Wl,--as-needed;

//Dependencies for the target
libstats_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;/usr/lib/x86_64-linux-gnu/libsqlite3.so;general;-Wl,--as-needed;

//Dependencies for the target
libtap-bridge_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;libinternet;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libtopology-read-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libtopology-read;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libtopology-read_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libtraffic-control-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libtraffic-control;general;libnetwork;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libtraffic-control_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libcore;general;-Wl,--as-needed;

//Dependencies for the target
libuan-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libuan;general;libnetwork;libmobility;libenergy;general;-Wl,--as-needed;

//Dependencies for the target
libuan_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libmobility;general;libenergy;general;-Wl,--as-needed;

//Dependencies for the target
libvirtual-net-device_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;-Wl,--as-needed;

//Dependencies for the target
libwave-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libwave;general;libcore;libpropagation;libwifi;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libwave_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libcore;general;libpropagation;general;libwifi;general;libinternet;general;-Wl,--as-needed;

//Dependencies for the target
libwifi-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libwifi;general;libnetwork;libpropagation;libenergy;libspectrum;libantenna;libmobility;/usr/lib/x86_64-linux-gnu/libgsl.so;/usr/lib/x86_64-linux-gnu/libgslcblas.so;general;-Wl,--as-needed;

//Dependencies for the target
libwifi_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libpropagation;general;libenergy;general;libspectrum;general;libantenna;general;libmobility;general;/usr/lib/x86_64-linux-gnu/libgsl.so;general;/usr/lib/x86_64-linux-gnu/libgslcblas.so;general;-Wl,--as-needed;

//Dependencies for the target
libwimax-test_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libwimax;general;libnetwork;libinternet;libpropagation;libmobility;general;-Wl,--as-needed;

//Dependencies for the target
libwimax_LIB_DEPENDS:STATIC=general;-Wl,--no-as-needed;general;libnetwork;general;libinternet;general;libpropagation;general;libmobility;general;-Wl,--as-needed;

//Path to a file.
openflow_header_internal_openflow.h:FILEPATH=openflow_header_internal_openflow.h-NOTFOUND

//Path to a library.
openflow_library_internal_openflow:FILEPATH=openflow_library_internal_openflow-NOTFOUND

//Path to a library.
pkgcfg_lib_GSL_gsl:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

//Path to a library.
pkgcfg_lib_GSL_gslcblas:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a library.
pkgcfg_lib_GSL_m:FILEPATH=/usr/lib/x86_64-linux-gnu/libm.so

//Path to a library.
pkgcfg_lib_PC_HARFBUZZ_harfbuzz:FILEPATH=/usr/lib/x86_64-linux-gnu/libharfbuzz.so

//Path to a library.
pkgcfg_lib_PC_LIBXML_xml2:FILEPATH=/usr/lib/x86_64-linux-gnu/libxml2.so


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CCACHE
CCACHE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//ADVANCED property for variable: CMAKE_FORMAT_PROGRAM
CMAKE_FORMAT_PROGRAM-ADVANCED:INTERNAL=1
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/ns3/ns-allinone-3.37/ns-3.37
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=99
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CONVERT
CONVERT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DIA
DIA-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOT
DOT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOXYGEN
DOXYGEN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOXYGEN_DOT_EXECUTABLE
DOXYGEN_DOT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DOXYGEN_EXECUTABLE
DOXYGEN_EXECUTABLE-ADVANCED:INTERNAL=1
DPDK_CFLAGS:INTERNAL=
DPDK_CFLAGS_I:INTERNAL=
DPDK_CFLAGS_OTHER:INTERNAL=
DPDK_FOUND:INTERNAL=
DPDK_INCLUDEDIR:INTERNAL=
DPDK_LIBDIR:INTERNAL=
DPDK_LIBS:INTERNAL=
DPDK_LIBS_L:INTERNAL=
DPDK_LIBS_OTHER:INTERNAL=
DPDK_LIBS_PATHS:INTERNAL=
DPDK_MODULE_NAME:INTERNAL=
DPDK_PREFIX:INTERNAL=
DPDK_STATIC_CFLAGS:INTERNAL=
DPDK_STATIC_CFLAGS_I:INTERNAL=
DPDK_STATIC_CFLAGS_OTHER:INTERNAL=
DPDK_STATIC_LIBDIR:INTERNAL=
DPDK_STATIC_LIBS:INTERNAL=
DPDK_STATIC_LIBS_L:INTERNAL=
DPDK_STATIC_LIBS_OTHER:INTERNAL=
DPDK_STATIC_LIBS_PATHS:INTERNAL=
DPDK_VERSION:INTERNAL=
DPDK_libdpdk_INCLUDEDIR:INTERNAL=
DPDK_libdpdk_LIBDIR:INTERNAL=
DPDK_libdpdk_PREFIX:INTERNAL=
DPDK_libdpdk_VERSION:INTERNAL=
//ADVANCED property for variable: DVIPNG
DVIPNG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ENABLE_DPDKDEVNET
ENABLE_DPDKDEVNET-ADVANCED:INTERNAL=1
ENABLE_DPDKDEVNET:INTERNAL=False
//ADVANCED property for variable: ENABLE_EMUNETDEV
ENABLE_EMUNETDEV-ADVANCED:INTERNAL=1
ENABLE_EMUNETDEV:INTERNAL=True
//ADVANCED property for variable: ENABLE_FDNETDEV
ENABLE_FDNETDEV-ADVANCED:INTERNAL=1
ENABLE_FDNETDEV:INTERNAL=True
//ADVANCED property for variable: ENABLE_NETMAP_EMU
ENABLE_NETMAP_EMU-ADVANCED:INTERNAL=1
ENABLE_NETMAP_EMU:INTERNAL=False
//ADVANCED property for variable: ENABLE_TAPNETDEV
ENABLE_TAPNETDEV-ADVANCED:INTERNAL=1
ENABLE_TAPNETDEV:INTERNAL=True
//ADVANCED property for variable: EPSTOPDF
EPSTOPDF-ADVANCED:INTERNAL=1
//Test FILESYSTEM_LIBRARY_IS_LINKED
FILESYSTEM_LIBRARY_IS_LINKED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake][c ][v1.74.0()]
//Details about finding GTK3_GTK
FIND_PACKAGE_MESSAGE_DETAILS_GTK3_GTK:INTERNAL=[/usr/lib/x86_64-linux-gnu/libgtk-3.so][/usr/include/gtk-3.0][/usr/include/glib-2.0][/usr/lib/x86_64-linux-gnu/glib-2.0/include][/usr/lib/x86_64-linux-gnu/libglib-2.0.so][/usr/lib/x86_64-linux-gnu/libgthread-2.0.so][/usr/include/gtk-3.0][/usr/include/gtk-3.0][/usr/lib/x86_64-linux-gnu/libgdk-3.so][v()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.2()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/bin/python3.10][/usr/include/python3.10][/usr/lib/x86_64-linux-gnu/libpython3.10.so][cfound components: Interpreter Development Development.Module Development.Embed ][v3.10.12()]
//Details about finding Sphinx
FIND_PACKAGE_MESSAGE_DETAILS_Sphinx:INTERNAL=[/usr/bin/sphinx-build][v()]
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_freetype2
FREETYPE_INCLUDE_DIR_freetype2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_INCLUDE_DIR_ft2build
FREETYPE_INCLUDE_DIR_ft2build-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_DEBUG
FREETYPE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FREETYPE_LIBRARY_RELEASE
FREETYPE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GSL_CBLAS_LIBRARY
GSL_CBLAS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GSL_CBLAS_LIBRARY_DEBUG
GSL_CBLAS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
GSL_CFLAGS:INTERNAL=
GSL_CFLAGS_I:INTERNAL=
GSL_CFLAGS_OTHER:INTERNAL=
GSL_FOUND:INTERNAL=1
GSL_INCLUDEDIR:INTERNAL=/usr/include
//ADVANCED property for variable: GSL_INCLUDE_DIR
GSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
GSL_INCLUDE_DIRS:INTERNAL=
GSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lgsl;-lgslcblas;-lm
GSL_LDFLAGS_OTHER:INTERNAL=
GSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_LIBRARIES:INTERNAL=gsl;gslcblas;m
//ADVANCED property for variable: GSL_LIBRARY
GSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GSL_LIBRARY_DEBUG
GSL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
GSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_LIBS:INTERNAL=
GSL_LIBS_L:INTERNAL=
GSL_LIBS_OTHER:INTERNAL=
GSL_LIBS_PATHS:INTERNAL=
GSL_MODULE_NAME:INTERNAL=gsl
GSL_PREFIX:INTERNAL=/usr
//ADVANCED property for variable: GSL_ROOT_DIR
GSL_ROOT_DIR-ADVANCED:INTERNAL=1
GSL_STATIC_CFLAGS:INTERNAL=
GSL_STATIC_CFLAGS_I:INTERNAL=
GSL_STATIC_CFLAGS_OTHER:INTERNAL=
GSL_STATIC_INCLUDE_DIRS:INTERNAL=
GSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lgsl;-lgslcblas;-lm
GSL_STATIC_LDFLAGS_OTHER:INTERNAL=
GSL_STATIC_LIBDIR:INTERNAL=
GSL_STATIC_LIBRARIES:INTERNAL=gsl;gslcblas;m
GSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_STATIC_LIBS:INTERNAL=
GSL_STATIC_LIBS_L:INTERNAL=
GSL_STATIC_LIBS_OTHER:INTERNAL=
GSL_STATIC_LIBS_PATHS:INTERNAL=
//ADVANCED property for variable: GSL_VERSION
GSL_VERSION-ADVANCED:INTERNAL=1
GSL_VERSION:INTERNAL=2.7.1
GSL_gsl_INCLUDEDIR:INTERNAL=
GSL_gsl_LIBDIR:INTERNAL=
GSL_gsl_PREFIX:INTERNAL=
GSL_gsl_VERSION:INTERNAL=
//ADVANCED property for variable: GTK3_ATK_INCLUDE_DIR
GTK3_ATK_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_ATK_LIBRARY
GTK3_ATK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_CAIRO_INCLUDE_DIR
GTK3_CAIRO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_CAIRO_LIBRARY
GTK3_CAIRO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_FONTCONFIG_INCLUDE_DIR
GTK3_FONTCONFIG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GDKCONFIG_INCLUDE_DIR
GTK3_GDKCONFIG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GDK_INCLUDE_DIR
GTK3_GDK_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GDK_LIBRARY
GTK3_GDK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GDK_PIXBUF_INCLUDE_DIR
GTK3_GDK_PIXBUF_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GDK_PIXBUF_LIBRARY
GTK3_GDK_PIXBUF_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GIO_INCLUDE_DIR
GTK3_GIO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GIO_LIBRARY
GTK3_GIO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GLIBCONFIG_INCLUDE_DIR
GTK3_GLIBCONFIG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GLIB_INCLUDE_DIR
GTK3_GLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GLIB_LIBRARY
GTK3_GLIB_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GOBJECT_INCLUDE_DIR
GTK3_GOBJECT_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GOBJECT_LIBRARY
GTK3_GOBJECT_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GTHREAD_LIBRARY
GTK3_GTHREAD_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GTK_INCLUDE_DIR
GTK3_GTK_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_GTK_LIBRARY
GTK3_GTK_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_HARFBUZZ_INCLUDE_DIR
GTK3_HARFBUZZ_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_PANGO_INCLUDE_DIR
GTK3_PANGO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTK3_PANGO_LIBRARY
GTK3_PANGO_LIBRARY-ADVANCED:INTERNAL=1
//Have include boost/units/quantity.hpp;boost/units/systems/si.hpp
HAVE_BOOST_UNITS:INTERNAL=1
//Have include dirent.h
HAVE_DIRENT_H:INTERNAL=1
//Have function getenv
HAVE_GETENV:INTERNAL=1
//Have include net/if.h
HAVE_IF_NETS_H:INTERNAL=1
//Have include linux/if_tun.h
HAVE_IF_TUN_H:INTERNAL=1
//Have include inttypes.h
HAVE_INTTYPES_H:INTERNAL=1
//Have include net/netmap_user.h
HAVE_NETMAP_USER_H:INTERNAL=
//Have include net/ethernet.h
HAVE_NET_ETHERNET_H:INTERNAL=1
//Have include netpacket/packet.h
HAVE_PACKETH:INTERNAL=1
//Have include netpacket/packet.h
HAVE_PACKET_H:INTERNAL=1
//Have include signal.h
HAVE_SIGNAL_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include stdlib.h
HAVE_STDLIB_H:INTERNAL=1
//Have include sys/ioctl.h
HAVE_SYS_IOCTL_H:INTERNAL=1
//Have include sys/stat.h
HAVE_SYS_STAT_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//Test HAVE_UINT128_T
HAVE_UINT128_T:INTERNAL=
//Test HAVE___UINT128_T
HAVE___UINT128_T:INTERNAL=1
//ADVANCED property for variable: HarfBuzz_INCLUDE_DIR
HarfBuzz_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: HarfBuzz_LIBRARY
HarfBuzz_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LATEXMK
LATEXMK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBXML2_INCLUDE_DIR
LIBXML2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBXML2_LIBRARY
LIBXML2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LIBXML2_XMLLINT_EXECUTABLE
LIBXML2_XMLLINT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: LLD
LLD-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MAKE
MAKE-ADVANCED:INTERNAL=1
//Test MISSING_OSTREAM_NULLPTR_OPERATOR
MISSING_OSTREAM_NULLPTR_OPERATOR:INTERNAL=
//ADVANCED property for variable: MOLD
MOLD-ADVANCED:INTERNAL=1
//ON if Brite is found in NS3_WITH_BRITE
NS3_BRITE:INTERNAL=OFF
//ON if Click is found in NS3_WITH_CLICK
NS3_CLICK:INTERNAL=OFF
//STRINGS property for variable: NS3_INT64X64
NS3_INT64X64-STRINGS:INTERNAL=INT128;CAIRO;DOUBLE
//ON if Openflow is found in NS3_WITH_OPENFLOW
NS3_OPENFLOW:INTERNAL=OFF
PC_HARFBUZZ_CFLAGS:INTERNAL=-I/usr/include/harfbuzz;-I/usr/include/glib-2.0;-I/usr/lib/x86_64-linux-gnu/glib-2.0/include;-I/usr/include/freetype2;-I/usr/include/libpng16
PC_HARFBUZZ_CFLAGS_I:INTERNAL=
PC_HARFBUZZ_CFLAGS_OTHER:INTERNAL=
PC_HARFBUZZ_FOUND:INTERNAL=1
PC_HARFBUZZ_INCLUDEDIR:INTERNAL=/usr/include
PC_HARFBUZZ_INCLUDE_DIRS:INTERNAL=/usr/include/harfbuzz;/usr/include/glib-2.0;/usr/lib/x86_64-linux-gnu/glib-2.0/include;/usr/include/freetype2;/usr/include/libpng16
PC_HARFBUZZ_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lharfbuzz
PC_HARFBUZZ_LDFLAGS_OTHER:INTERNAL=
PC_HARFBUZZ_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_HARFBUZZ_LIBRARIES:INTERNAL=harfbuzz
PC_HARFBUZZ_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_HARFBUZZ_LIBS:INTERNAL=
PC_HARFBUZZ_LIBS_L:INTERNAL=
PC_HARFBUZZ_LIBS_OTHER:INTERNAL=
PC_HARFBUZZ_LIBS_PATHS:INTERNAL=
PC_HARFBUZZ_MODULE_NAME:INTERNAL=harfbuzz
PC_HARFBUZZ_PREFIX:INTERNAL=/usr
PC_HARFBUZZ_STATIC_CFLAGS:INTERNAL=-I/usr/include/harfbuzz;-I/usr/include/glib-2.0;-I/usr/lib/x86_64-linux-gnu/glib-2.0/include;-I/usr/include/freetype2;-I/usr/include/libpng16
PC_HARFBUZZ_STATIC_CFLAGS_I:INTERNAL=
PC_HARFBUZZ_STATIC_CFLAGS_OTHER:INTERNAL=
PC_HARFBUZZ_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/harfbuzz;/usr/include/glib-2.0;/usr/lib/x86_64-linux-gnu/glib-2.0/include;/usr/include/freetype2;/usr/include/libpng16
PC_HARFBUZZ_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lharfbuzz;-lm;-lglib-2.0;-pthread;-lm;-lpcre;-lfreetype;-lpng16;-lm;-lz;-lm;-lz;-lbrotlidec;-lbrotlicommon;-lgraphite2
PC_HARFBUZZ_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
PC_HARFBUZZ_STATIC_LIBDIR:INTERNAL=
PC_HARFBUZZ_STATIC_LIBRARIES:INTERNAL=harfbuzz;m;glib-2.0;m;pcre;freetype;png16;m;z;m;z;brotlidec;brotlicommon;graphite2
PC_HARFBUZZ_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_HARFBUZZ_STATIC_LIBS:INTERNAL=
PC_HARFBUZZ_STATIC_LIBS_L:INTERNAL=
PC_HARFBUZZ_STATIC_LIBS_OTHER:INTERNAL=
PC_HARFBUZZ_STATIC_LIBS_PATHS:INTERNAL=
PC_HARFBUZZ_VERSION:INTERNAL=2.7.4
PC_HARFBUZZ_harfbuzz_INCLUDEDIR:INTERNAL=
PC_HARFBUZZ_harfbuzz_LIBDIR:INTERNAL=
PC_HARFBUZZ_harfbuzz_PREFIX:INTERNAL=
PC_HARFBUZZ_harfbuzz_VERSION:INTERNAL=
PC_LIBXML_CFLAGS:INTERNAL=-I/usr/include/libxml2
PC_LIBXML_CFLAGS_I:INTERNAL=
PC_LIBXML_CFLAGS_OTHER:INTERNAL=
PC_LIBXML_FOUND:INTERNAL=1
PC_LIBXML_INCLUDEDIR:INTERNAL=/usr/include
PC_LIBXML_INCLUDE_DIRS:INTERNAL=/usr/include/libxml2
PC_LIBXML_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lxml2
PC_LIBXML_LDFLAGS_OTHER:INTERNAL=
PC_LIBXML_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBXML_LIBRARIES:INTERNAL=xml2
PC_LIBXML_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBXML_LIBS:INTERNAL=
PC_LIBXML_LIBS_L:INTERNAL=
PC_LIBXML_LIBS_OTHER:INTERNAL=
PC_LIBXML_LIBS_PATHS:INTERNAL=
PC_LIBXML_MODULE_NAME:INTERNAL=libxml-2.0
PC_LIBXML_PREFIX:INTERNAL=/usr
PC_LIBXML_STATIC_CFLAGS:INTERNAL=-I/usr/include/libxml2
PC_LIBXML_STATIC_CFLAGS_I:INTERNAL=
PC_LIBXML_STATIC_CFLAGS_OTHER:INTERNAL=
PC_LIBXML_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/libxml2
PC_LIBXML_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lxml2;-licui18n;-licuuc;-licudata;-lz;-llzma;-lm
PC_LIBXML_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_LIBXML_STATIC_LIBDIR:INTERNAL=
PC_LIBXML_STATIC_LIBRARIES:INTERNAL=xml2;icui18n;icuuc;icudata;z;lzma;m
PC_LIBXML_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBXML_STATIC_LIBS:INTERNAL=
PC_LIBXML_STATIC_LIBS_L:INTERNAL=
PC_LIBXML_STATIC_LIBS_OTHER:INTERNAL=
PC_LIBXML_STATIC_LIBS_PATHS:INTERNAL=
PC_LIBXML_VERSION:INTERNAL=2.9.13
PC_LIBXML_libxml-2.0_INCLUDEDIR:INTERNAL=
PC_LIBXML_libxml-2.0_LIBDIR:INTERNAL=
PC_LIBXML_libxml-2.0_PREFIX:INTERNAL=
PC_LIBXML_libxml-2.0_VERSION:INTERNAL=
//ADVANCED property for variable: PDFLATEX
PDFLATEX-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PYTHON3
PYTHON3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ProcessorCount_cmd_nproc
ProcessorCount_cmd_nproc-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ProcessorCount_cmd_sysctl
ProcessorCount_cmd_sysctl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPHINX_EXECUTABLE
SPHINX_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPHINX_OUTPUT_HTML
SPHINX_OUTPUT_HTML-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPHINX_OUTPUT_MAN
SPHINX_OUTPUT_MAN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPHINX_WARNINGS_AS_ERRORS
SPHINX_WARNINGS_AS_ERRORS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SQLite3_header_internal_sqlite3.h
SQLite3_header_internal_sqlite3.h-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SQLite3_library_internal_sqlite3
SQLite3_library_internal_sqlite3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: VALGRIND
VALGRIND-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
_Python3_DEVELOPMENT_EMBED_SIGNATURE:INTERNAL=625d9e0f7a1af1ddb80bfa11fbea90e0
_Python3_DEVELOPMENT_MODULE_SIGNATURE:INTERNAL=4bc14a0ea6a21ea267e38aa660e9b54b
//Path to a program.
_Python3_EXECUTABLE:INTERNAL=/usr/bin/python3.10
//Path to a file.
_Python3_INCLUDE_DIR:INTERNAL=/usr/include/python3.10
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;10;12;64;;cpython-310-x86_64-linux-gnu;/usr/lib/python3.10;/usr/lib/python3.10;/usr/lib/python3/dist-packages;/usr/lib/python3/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=b91b4035ecb3bb3f5760d35922531bcd
//Path to a library.
_Python3_LIBRARY_RELEASE:INTERNAL=/usr/lib/x86_64-linux-gnu/libpython3.10.so
__pkg_config_arguments_GSL:INTERNAL=QUIET;gsl
__pkg_config_arguments_PC_HARFBUZZ:INTERNAL=QUIET;harfbuzz
__pkg_config_arguments_PC_LIBXML:INTERNAL=QUIET;libxml-2.0
__pkg_config_checked_DPDK:INTERNAL=1
__pkg_config_checked_GSL:INTERNAL=1
__pkg_config_checked_PC_HARFBUZZ:INTERNAL=1
__pkg_config_checked_PC_LIBXML:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: brite_header_internal_Brite.h
brite_header_internal_Brite.h-ADVANCED:INTERNAL=1
//ADVANCED property for variable: brite_library_internal_brite
brite_library_internal_brite-ADVANCED:INTERNAL=1
//ADVANCED property for variable: build_profile
build_profile-ADVANCED:INTERNAL=1
build_profile:INTERNAL=debug
//ADVANCED property for variable: build_profile_suffix
build_profile_suffix-ADVANCED:INTERNAL=1
build_profile_suffix:INTERNAL=-debug
//ADVANCED property for variable: click_header_internal_simclick.h
click_header_internal_simclick.h-ADVANCED:INTERNAL=1
//ADVANCED property for variable: click_library_internal_click
click_library_internal_click-ADVANCED:INTERNAL=1
//ADVANCED property for variable: click_library_internal_nsclick
click_library_internal_nsclick-ADVANCED:INTERNAL=1
//ADVANCED property for variable: libantenna
libantenna-ADVANCED:INTERNAL=1
libantenna:INTERNAL=libantenna
//ADVANCED property for variable: libantenna-obj
libantenna-obj-ADVANCED:INTERNAL=1
libantenna-obj:INTERNAL=libantenna-obj
//ADVANCED property for variable: libaodv
libaodv-ADVANCED:INTERNAL=1
libaodv:INTERNAL=libaodv
//ADVANCED property for variable: libaodv-obj
libaodv-obj-ADVANCED:INTERNAL=1
libaodv-obj:INTERNAL=libaodv-obj
//ADVANCED property for variable: libapplications
libapplications-ADVANCED:INTERNAL=1
libapplications:INTERNAL=libapplications
//ADVANCED property for variable: libapplications-obj
libapplications-obj-ADVANCED:INTERNAL=1
libapplications-obj:INTERNAL=libapplications-obj
//ADVANCED property for variable: libbridge
libbridge-ADVANCED:INTERNAL=1
libbridge:INTERNAL=libbridge
//ADVANCED property for variable: libbridge-obj
libbridge-obj-ADVANCED:INTERNAL=1
libbridge-obj:INTERNAL=libbridge-obj
//ADVANCED property for variable: libbrite
libbrite-ADVANCED:INTERNAL=1
libbrite:INTERNAL=libbrite
//ADVANCED property for variable: libbrite-obj
libbrite-obj-ADVANCED:INTERNAL=1
libbrite-obj:INTERNAL=libbrite-obj
//ADVANCED property for variable: libbuildings
libbuildings-ADVANCED:INTERNAL=1
libbuildings:INTERNAL=libbuildings
//ADVANCED property for variable: libbuildings-obj
libbuildings-obj-ADVANCED:INTERNAL=1
libbuildings-obj:INTERNAL=libbuildings-obj
//ADVANCED property for variable: libclick
libclick-ADVANCED:INTERNAL=1
libclick:INTERNAL=libclick
//ADVANCED property for variable: libclick-obj
libclick-obj-ADVANCED:INTERNAL=1
libclick-obj:INTERNAL=libclick-obj
//ADVANCED property for variable: libconfig-store
libconfig-store-ADVANCED:INTERNAL=1
libconfig-store:INTERNAL=libconfig-store
//ADVANCED property for variable: libconfig-store-obj
libconfig-store-obj-ADVANCED:INTERNAL=1
libconfig-store-obj:INTERNAL=libconfig-store-obj
//ADVANCED property for variable: libcore
libcore-ADVANCED:INTERNAL=1
libcore:INTERNAL=libcore
//ADVANCED property for variable: libcore-obj
libcore-obj-ADVANCED:INTERNAL=1
libcore-obj:INTERNAL=libcore-obj
//ADVANCED property for variable: libcsma
libcsma-ADVANCED:INTERNAL=1
libcsma:INTERNAL=libcsma
//ADVANCED property for variable: libcsma-layout
libcsma-layout-ADVANCED:INTERNAL=1
libcsma-layout:INTERNAL=libcsma-layout
//ADVANCED property for variable: libcsma-layout-obj
libcsma-layout-obj-ADVANCED:INTERNAL=1
libcsma-layout-obj:INTERNAL=libcsma-layout-obj
//ADVANCED property for variable: libcsma-obj
libcsma-obj-ADVANCED:INTERNAL=1
libcsma-obj:INTERNAL=libcsma-obj
//ADVANCED property for variable: libdsdv
libdsdv-ADVANCED:INTERNAL=1
libdsdv:INTERNAL=libdsdv
//ADVANCED property for variable: libdsdv-obj
libdsdv-obj-ADVANCED:INTERNAL=1
libdsdv-obj:INTERNAL=libdsdv-obj
//ADVANCED property for variable: libdsr
libdsr-ADVANCED:INTERNAL=1
libdsr:INTERNAL=libdsr
//ADVANCED property for variable: libdsr-obj
libdsr-obj-ADVANCED:INTERNAL=1
libdsr-obj:INTERNAL=libdsr-obj
//ADVANCED property for variable: libenergy
libenergy-ADVANCED:INTERNAL=1
libenergy:INTERNAL=libenergy
//ADVANCED property for variable: libenergy-obj
libenergy-obj-ADVANCED:INTERNAL=1
libenergy-obj:INTERNAL=libenergy-obj
//ADVANCED property for variable: libfd-net-device
libfd-net-device-ADVANCED:INTERNAL=1
libfd-net-device:INTERNAL=libfd-net-device
//ADVANCED property for variable: libfd-net-device-obj
libfd-net-device-obj-ADVANCED:INTERNAL=1
libfd-net-device-obj:INTERNAL=libfd-net-device-obj
//ADVANCED property for variable: libflow-monitor
libflow-monitor-ADVANCED:INTERNAL=1
libflow-monitor:INTERNAL=libflow-monitor
//ADVANCED property for variable: libflow-monitor-obj
libflow-monitor-obj-ADVANCED:INTERNAL=1
libflow-monitor-obj:INTERNAL=libflow-monitor-obj
//ADVANCED property for variable: libinternet
libinternet-ADVANCED:INTERNAL=1
libinternet:INTERNAL=libinternet
//ADVANCED property for variable: libinternet-apps
libinternet-apps-ADVANCED:INTERNAL=1
libinternet-apps:INTERNAL=libinternet-apps
//ADVANCED property for variable: libinternet-apps-obj
libinternet-apps-obj-ADVANCED:INTERNAL=1
libinternet-apps-obj:INTERNAL=libinternet-apps-obj
//ADVANCED property for variable: libinternet-obj
libinternet-obj-ADVANCED:INTERNAL=1
libinternet-obj:INTERNAL=libinternet-obj
//ADVANCED property for variable: liblr-wpan
liblr-wpan-ADVANCED:INTERNAL=1
liblr-wpan:INTERNAL=liblr-wpan
//ADVANCED property for variable: liblr-wpan-obj
liblr-wpan-obj-ADVANCED:INTERNAL=1
liblr-wpan-obj:INTERNAL=liblr-wpan-obj
//ADVANCED property for variable: liblte
liblte-ADVANCED:INTERNAL=1
liblte:INTERNAL=liblte
//ADVANCED property for variable: liblte-obj
liblte-obj-ADVANCED:INTERNAL=1
liblte-obj:INTERNAL=liblte-obj
//ADVANCED property for variable: libmesh
libmesh-ADVANCED:INTERNAL=1
libmesh:INTERNAL=libmesh
//ADVANCED property for variable: libmesh-obj
libmesh-obj-ADVANCED:INTERNAL=1
libmesh-obj:INTERNAL=libmesh-obj
//ADVANCED property for variable: libmobility
libmobility-ADVANCED:INTERNAL=1
libmobility:INTERNAL=libmobility
//ADVANCED property for variable: libmobility-obj
libmobility-obj-ADVANCED:INTERNAL=1
libmobility-obj:INTERNAL=libmobility-obj
//ADVANCED property for variable: libmpi
libmpi-ADVANCED:INTERNAL=1
libmpi:INTERNAL=libmpi
//ADVANCED property for variable: libmpi-obj
libmpi-obj-ADVANCED:INTERNAL=1
libmpi-obj:INTERNAL=libmpi-obj
//ADVANCED property for variable: libnetanim
libnetanim-ADVANCED:INTERNAL=1
libnetanim:INTERNAL=libnetanim
//ADVANCED property for variable: libnetanim-obj
libnetanim-obj-ADVANCED:INTERNAL=1
libnetanim-obj:INTERNAL=libnetanim-obj
//ADVANCED property for variable: libnetwork
libnetwork-ADVANCED:INTERNAL=1
libnetwork:INTERNAL=libnetwork
//ADVANCED property for variable: libnetwork-obj
libnetwork-obj-ADVANCED:INTERNAL=1
libnetwork-obj:INTERNAL=libnetwork-obj
//ADVANCED property for variable: libnix-vector-routing
libnix-vector-routing-ADVANCED:INTERNAL=1
libnix-vector-routing:INTERNAL=libnix-vector-routing
//ADVANCED property for variable: libnix-vector-routing-obj
libnix-vector-routing-obj-ADVANCED:INTERNAL=1
libnix-vector-routing-obj:INTERNAL=libnix-vector-routing-obj
//ADVANCED property for variable: libolsr
libolsr-ADVANCED:INTERNAL=1
libolsr:INTERNAL=libolsr
//ADVANCED property for variable: libolsr-obj
libolsr-obj-ADVANCED:INTERNAL=1
libolsr-obj:INTERNAL=libolsr-obj
//ADVANCED property for variable: libopenflow
libopenflow-ADVANCED:INTERNAL=1
libopenflow:INTERNAL=libopenflow
//ADVANCED property for variable: libopenflow-obj
libopenflow-obj-ADVANCED:INTERNAL=1
libopenflow-obj:INTERNAL=libopenflow-obj
//ADVANCED property for variable: libpoint-to-point
libpoint-to-point-ADVANCED:INTERNAL=1
libpoint-to-point:INTERNAL=libpoint-to-point
//ADVANCED property for variable: libpoint-to-point-layout
libpoint-to-point-layout-ADVANCED:INTERNAL=1
libpoint-to-point-layout:INTERNAL=libpoint-to-point-layout
//ADVANCED property for variable: libpoint-to-point-layout-obj
libpoint-to-point-layout-obj-ADVANCED:INTERNAL=1
libpoint-to-point-layout-obj:INTERNAL=libpoint-to-point-layout-obj
//ADVANCED property for variable: libpoint-to-point-obj
libpoint-to-point-obj-ADVANCED:INTERNAL=1
libpoint-to-point-obj:INTERNAL=libpoint-to-point-obj
//ADVANCED property for variable: libpropagation
libpropagation-ADVANCED:INTERNAL=1
libpropagation:INTERNAL=libpropagation
//ADVANCED property for variable: libpropagation-obj
libpropagation-obj-ADVANCED:INTERNAL=1
libpropagation-obj:INTERNAL=libpropagation-obj
//ADVANCED property for variable: libsixlowpan
libsixlowpan-ADVANCED:INTERNAL=1
libsixlowpan:INTERNAL=libsixlowpan
//ADVANCED property for variable: libsixlowpan-obj
libsixlowpan-obj-ADVANCED:INTERNAL=1
libsixlowpan-obj:INTERNAL=libsixlowpan-obj
//ADVANCED property for variable: libspectrum
libspectrum-ADVANCED:INTERNAL=1
libspectrum:INTERNAL=libspectrum
//ADVANCED property for variable: libspectrum-obj
libspectrum-obj-ADVANCED:INTERNAL=1
libspectrum-obj:INTERNAL=libspectrum-obj
//ADVANCED property for variable: libstats
libstats-ADVANCED:INTERNAL=1
libstats:INTERNAL=libstats
//ADVANCED property for variable: libstats-obj
libstats-obj-ADVANCED:INTERNAL=1
libstats-obj:INTERNAL=libstats-obj
//ADVANCED property for variable: libtap-bridge
libtap-bridge-ADVANCED:INTERNAL=1
libtap-bridge:INTERNAL=libtap-bridge
//ADVANCED property for variable: libtap-bridge-obj
libtap-bridge-obj-ADVANCED:INTERNAL=1
libtap-bridge-obj:INTERNAL=libtap-bridge-obj
//ADVANCED property for variable: libtest
libtest-ADVANCED:INTERNAL=1
libtest:INTERNAL=libtest
//ADVANCED property for variable: libtest-obj
libtest-obj-ADVANCED:INTERNAL=1
libtest-obj:INTERNAL=libtest-obj
//ADVANCED property for variable: libtopology-read
libtopology-read-ADVANCED:INTERNAL=1
libtopology-read:INTERNAL=libtopology-read
//ADVANCED property for variable: libtopology-read-obj
libtopology-read-obj-ADVANCED:INTERNAL=1
libtopology-read-obj:INTERNAL=libtopology-read-obj
//ADVANCED property for variable: libtraffic-control
libtraffic-control-ADVANCED:INTERNAL=1
libtraffic-control:INTERNAL=libtraffic-control
//ADVANCED property for variable: libtraffic-control-obj
libtraffic-control-obj-ADVANCED:INTERNAL=1
libtraffic-control-obj:INTERNAL=libtraffic-control-obj
//ADVANCED property for variable: libuan
libuan-ADVANCED:INTERNAL=1
libuan:INTERNAL=libuan
//ADVANCED property for variable: libuan-obj
libuan-obj-ADVANCED:INTERNAL=1
libuan-obj:INTERNAL=libuan-obj
//ADVANCED property for variable: libvirtual-net-device
libvirtual-net-device-ADVANCED:INTERNAL=1
libvirtual-net-device:INTERNAL=libvirtual-net-device
//ADVANCED property for variable: libvirtual-net-device-obj
libvirtual-net-device-obj-ADVANCED:INTERNAL=1
libvirtual-net-device-obj:INTERNAL=libvirtual-net-device-obj
//ADVANCED property for variable: libvisualizer
libvisualizer-ADVANCED:INTERNAL=1
libvisualizer:INTERNAL=libvisualizer
//ADVANCED property for variable: libvisualizer-obj
libvisualizer-obj-ADVANCED:INTERNAL=1
libvisualizer-obj:INTERNAL=libvisualizer-obj
//ADVANCED property for variable: libwave
libwave-ADVANCED:INTERNAL=1
libwave:INTERNAL=libwave
//ADVANCED property for variable: libwave-obj
libwave-obj-ADVANCED:INTERNAL=1
libwave-obj:INTERNAL=libwave-obj
//ADVANCED property for variable: libwifi
libwifi-ADVANCED:INTERNAL=1
libwifi:INTERNAL=libwifi
//ADVANCED property for variable: libwifi-obj
libwifi-obj-ADVANCED:INTERNAL=1
libwifi-obj:INTERNAL=libwifi-obj
//ADVANCED property for variable: libwimax
libwimax-ADVANCED:INTERNAL=1
libwimax:INTERNAL=libwimax
//ADVANCED property for variable: libwimax-obj
libwimax-obj-ADVANCED:INTERNAL=1
libwimax-obj:INTERNAL=libwimax-obj
//ADVANCED property for variable: ns3-all-enabled-modules
ns3-all-enabled-modules-ADVANCED:INTERNAL=1
//list with all enabled modules
ns3-all-enabled-modules:INTERNAL=antenna;aodv;applications;bridge;brite;buildings;click;config-store;core;csma;csma-layout;dsdv;dsr;energy;fd-net-device;flow-monitor;internet;internet-apps;lr-wpan;lte;mesh;mobility;mpi;netanim;network;nix-vector-routing;olsr;openflow;point-to-point;point-to-point-layout;propagation;sixlowpan;spectrum;stats;tap-bridge;test;topology-read;traffic-control;uan;virtual-net-device;visualizer;wave;wifi;wimax;
//ADVANCED property for variable: ns3-example-folders
ns3-example-folders-ADVANCED:INTERNAL=1
//list of example folders
ns3-example-folders:INTERNAL=wireless;udp-client-server;udp;tutorial;traffic-control;tcp;stats;socket;routing;realtime;naming;matrix-topology;ipv6;error-model;energy;channel-models;
//ADVANCED property for variable: ns3-execs
ns3-execs-ADVANCED:INTERNAL=1
//list of c++ executables
ns3-execs:INTERNAL=/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/perf/ns3.37-perf-io-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-print-introspected-doxygen-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-bench-packets-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-bench-scheduler-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-test-runner-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/subdir/ns3.37-scratch-subdir-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-underwater-relay-simulation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-uav_3d-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-uan-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-simple_test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-scratch-simulator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-icmp1-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-icmp-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-first-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-bless-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-apnotuse-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-apnotuse-kehu-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-eht-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-wired-bridging-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-vht-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-txop-aggregation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-timing-attributes-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-tcp-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-saturation-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-per-interference-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-per-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spatial-reuse-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-sleep-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-interference-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-infra-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-ht-hidden-stations-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-adhoc-grid-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-adhoc-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-rate-adaptation-distance-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-power-adaptation-interference-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-power-adaptation-distance-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-error-models-comparison-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-vht-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-ht-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-he-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-multirate-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-multi-tos-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-mixed-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ht-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-hidden-terminal-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-he-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-dsss-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-clear-channel-cmu-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-blockack-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-backward-compatibility-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ap-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-aggregation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-adhoc-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-80211n-mimo-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-80211e-txop-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-mixed-wired-wireless-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp-client-server/ns3.37-udp-trace-client-server-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp-client-server/ns3.37-udp-client-server-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp/ns3.37-udp-echo-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-seventh-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-sixth-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-fifth-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-fourth-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-third-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-second-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-first-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-hello-simulator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-cobalt-vs-codel-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-tbf-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-red-vs-nlred-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-red-vs-fengadaptive-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-queue-discs-benchmark-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-traffic-control-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-dctcp-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-validation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-linux-reno-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-pacing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-variants-comparison-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-pcap-nanosec-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-bulk-send-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-bbr-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-star-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-star-server-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-large-transfer-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/stats/ns3.37-wifi-example-sim-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-options-ipv6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-options-ipv4-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-bound-tcp-static-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-bound-static-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-multicast-flooding-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-routing-multi-switch-plus-router-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-rip-simple-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-ripng-simple-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-manet-routing-compare-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-routing-ping6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-mixed-global-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-alternate-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-global-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-injection-slash32-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-routing-slash32-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-static-routing-slash32-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-dynamic-global-routing-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/realtime/ns3.37-realtime-udp-echo-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/naming/ns3.37-object-names-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/matrix-topology/ns3.37-matrix-topology-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-PMTU-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-wsn-ping6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-test-ipv6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-radvd-two-prefix-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-radvd-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-ping6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-loose-routing-ipv6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-icmpv6-redirect-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-two-MTU-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/error-model/ns3.37-simple-error-model-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/energy/ns3.37-energy-model-with-harvesting-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/energy/ns3.37-energy-model-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/channel-models/ns3.37-three-gpp-v2v-channel-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-simple-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-multicast-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-ipv4-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-bianchi-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-phy-configuration-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-trans-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-manager-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-test-interference-helper-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-phy-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-vanet-routing-compare-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-wave-simple-device-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-wave-simple-80211p-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/virtual-net-device/examples/ns3.37-virtual-net-device-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-6lowpan-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-raw-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-ipv6-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-ipv4-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-rc-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-cw-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-fqcodel-l4s-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-pie-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-codel-vs-pfifo-asymmetric-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-codel-vs-pfifo-basic-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-pfifo-vs-red-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-adaptive-red-tests-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-red-vs-ared-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-red-tests-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/topology-read/examples/ns3.37-topology-example-sim-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/ns3.37-tap-creator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-wifi-dumbbell-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-wifi-virtual-machine-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-csma-virtual-machine-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-csma-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-file-helper-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-file-aggregator-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-helper-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-aggregator-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-double-probe-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-time-probe-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-three-gpp-channel-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-tv-trans-regional-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-tv-trans-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-with-microwave-oven-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-matrix-propagation-loss-model-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-mesh-under-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-beacon-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-sixlowpan-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/propagation/examples/ns3.37-jakes-propagation-model-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/propagation/examples/ns3.37-main-propagation-loss-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/point-to-point/examples/ns3.37-main-attribute-value-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/olsr/examples/ns3.37-simple-point-to-point-olsr-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/olsr/examples/ns3.37-olsr-hna-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-double-wifi-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nms-p2p-nix-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-simple-multi-address-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-simple-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-lollipop-comparisions-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-packet-socket-apps-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-main-packet-tag-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-main-packet-header-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-bit-serializer-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-uan-animation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-wireless-animation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-resources-counters-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-colors-link-description-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-star-animation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-grid-animation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-dumbbell-animation-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-reference-point-group-mobility-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-mobility-trace-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-grid-topology-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-ns2-mobility-trace-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-random-walk-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-random-topology-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-bonnmotion-ns2-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mesh/examples/ns3.37-mesh-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-emu-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-x2-handover-measures-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-x2-handover-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-uplink-power-control-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-backhaul-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rlc-traces-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rem-sector-antenna-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rem-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-radio-link-failure-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-profiling-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-pathloss-traces-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-ue-ue-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-ue-rh-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-addr-conf-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-intercell-interference-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-frequency-reuse-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-fading-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-dual-stripe-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-distributed-ffr-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-deactivate-bearer-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-cqi-threshold-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-cc-helper-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-bootstrap-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-error-model-plot-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-error-distance-plot-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-active-scan-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-ed-scan-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-phy-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-packet-print-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-mlme-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-data-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet-apps/examples/ns3.37-traceroute-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet-apps/examples/ns3.37-dhcp-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-neighbor-cache-dynamic-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-neighbor-cache-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-main-simple-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-tap-ping6-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-tap-ping-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-tc-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-send-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-onoff-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-udp-echo-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-ping-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-realtime-fd2fd-onoff-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-realtime-dummy-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd2fd-onoff-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-dummy-network-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/ns3.37-tap-device-creator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/ns3.37-raw-sock-creator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-basic-energy-model-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-rv-battery-model-test-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-li-ion-energy-source-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/dsr/examples/ns3.37-dsr-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/dsdv/examples/ns3.37-dsdv-manet-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma-layout/examples/ns3.37-csma-star-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-ping-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-raw-ip-socket-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-multicast-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-packet-socket-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-broadcast-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-one-subnet-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-empirical-random-variable-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-test-sync-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-random-variable-stream-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-test-string-value-formatting-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-system-path-examples-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-simulator-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-show-progress-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-random-variable-stream-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-random-variable-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-log-time-format-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-ptr-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-callback-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-length-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-hash-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-fatal-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-command-line-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/config-store/examples/ns3.37-config-store-save-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-outdoor-random-walk-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-outdoor-group-mobility-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-buildings-pathloss-profiler-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/bridge/examples/ns3.37-csma-bridge-one-hop-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/bridge/examples/ns3.37-csma-bridge-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/applications/examples/ns3.37-three-gpp-http-example-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/aodv/examples/ns3.37-aodv-debug;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/ns3.37-stdlib_pch_exec-debug;
//ADVANCED property for variable: ns3-execs-clean
ns3-execs-clean-ADVANCED:INTERNAL=1
//list of c++ executables without version prefix and build suffix
ns3-execs-clean:INTERNAL=perf-io;print-introspected-doxygen;bench-packets;bench-scheduler;test-runner;scratch_subdir_scratch-subdir;scratch_underwater-relay-simulation;scratch_uav_3d;scratch_uan-test;scratch_simple_test;scratch_scratch-simulator;scratch_icmp1;scratch_icmp;scratch_first;scratch_bless;scratch_apnotuse;scratch_apnotuse-kehu;wifi-ofdm-eht-validation;wifi-wired-bridging;wifi-vht-network;wifi-txop-aggregation;wifi-timing-attributes;wifi-tcp;wifi-spectrum-saturation-example;wifi-spectrum-per-interference;wifi-spectrum-per-example;wifi-spatial-reuse;wifi-sleep;wifi-simple-interference;wifi-simple-infra;wifi-simple-ht-hidden-stations;wifi-simple-adhoc-grid;wifi-simple-adhoc;wifi-rate-adaptation-distance;wifi-power-adaptation-interference;wifi-power-adaptation-distance;wifi-error-models-comparison;wifi-ofdm-vht-validation;wifi-ofdm-validation;wifi-ofdm-ht-validation;wifi-ofdm-he-validation;wifi-multirate;wifi-multi-tos;wifi-mixed-network;wifi-ht-network;wifi-hidden-terminal;wifi-he-network;wifi-dsss-validation;wifi-clear-channel-cmu;wifi-blockack;wifi-backward-compatibility;wifi-ap;wifi-aggregation;wifi-adhoc;wifi-80211n-mimo;wifi-80211e-txop;mixed-wired-wireless;udp-trace-client-server;udp-client-server;udp-echo;seventh;sixth;fifth;fourth;third;second;first;hello-simulator;cobalt-vs-codel;tbf-example;red-vs-nlred;red-vs-fengadaptive;queue-discs-benchmark;traffic-control;dctcp-example;tcp-validation;tcp-linux-reno;tcp-pacing;tcp-variants-comparison;tcp-pcap-nanosec-example;tcp-bulk-send;tcp-bbr-example;star;tcp-star-server;tcp-large-transfer;wifi-example-sim;socket-options-ipv6;socket-options-ipv4;socket-bound-tcp-static-routing;socket-bound-static-routing;simple-multicast-flooding;global-routing-multi-switch-plus-router;rip-simple-network;ripng-simple-network;manet-routing-compare;simple-routing-ping6;mixed-global-routing;simple-alternate-routing;simple-global-routing;global-injection-slash32;global-routing-slash32;static-routing-slash32;dynamic-global-routing;realtime-udp-echo;object-names;matrix-topology;fragmentation-ipv6-PMTU;wsn-ping6;test-ipv6;radvd-two-prefix;radvd;ping6;loose-routing-ipv6;icmpv6-redirect;fragmentation-ipv6-two-MTU;fragmentation-ipv6;simple-error-model;energy-model-with-harvesting-example;energy-model-example;three-gpp-v2v-channel-example;wimax-simple;wimax-multicast;wimax-ipv4;wifi-bianchi;wifi-phy-configuration;wifi-trans-example;wifi-manager-example;wifi-test-interference-helper;wifi-phy-test;vanet-routing-compare;wave-simple-device;wave-simple-80211p;virtual-net-device;uan-6lowpan-example;uan-raw-example;uan-ipv6-example;uan-ipv4-example;uan-rc-example;uan-cw-example;fqcodel-l4s-example;pie-example;codel-vs-pfifo-asymmetric;codel-vs-pfifo-basic-test;pfifo-vs-red;adaptive-red-tests;red-vs-ared;red-tests;topology-example-sim;tap-creator;tap-wifi-dumbbell;tap-wifi-virtual-machine;tap-csma-virtual-machine;tap-csma;file-helper-example;file-aggregator-example;gnuplot-helper-example;gnuplot-aggregator-example;double-probe-example;gnuplot-example;time-probe-example;three-gpp-channel-example;tv-trans-regional-example;tv-trans-example;adhoc-aloha-ideal-phy-with-microwave-oven;adhoc-aloha-ideal-phy-matrix-propagation-loss-model;adhoc-aloha-ideal-phy;example-ping-lr-wpan-mesh-under;example-ping-lr-wpan-beacon;example-ping-lr-wpan;example-sixlowpan;jakes-propagation-model-example;main-propagation-loss;main-attribute-value;simple-point-to-point-olsr;olsr-hna;nix-double-wifi;nms-p2p-nix;nix-simple-multi-address;nix-simple;lollipop-comparisions;packet-socket-apps;main-packet-tag;main-packet-header;bit-serializer;uan-animation;wireless-animation;resources-counters;colors-link-description;star-animation;grid-animation;dumbbell-animation;reference-point-group-mobility-example;mobility-trace-example;main-grid-topology;ns2-mobility-trace;main-random-walk;main-random-topology;bonnmotion-ns2-example;mesh;lena-simple-epc-emu;lena-x2-handover-measures;lena-x2-handover;lena-uplink-power-control;lena-simple-epc-backhaul;lena-simple-epc;lena-simple;lena-rlc-traces;lena-rem-sector-antenna;lena-rem;lena-radio-link-failure;lena-profiling;lena-pathloss-traces;lena-ipv6-ue-ue;lena-ipv6-ue-rh;lena-ipv6-addr-conf;lena-intercell-interference;lena-frequency-reuse;lena-fading;lena-dual-stripe;lena-distributed-ffr;lena-deactivate-bearer;lena-cqi-threshold;lena-cc-helper;lr-wpan-bootstrap;lr-wpan-error-model-plot;lr-wpan-error-distance-plot;lr-wpan-active-scan;lr-wpan-ed-scan;lr-wpan-phy-test;lr-wpan-packet-print;lr-wpan-mlme;lr-wpan-data;traceroute-example;dhcp-example;neighbor-cache-dynamic;neighbor-cache-example;main-simple;fd-tap-ping6;fd-tap-ping;fd-emu-tc;fd-emu-send;fd-emu-onoff;fd-emu-udp-echo;fd-emu-ping;realtime-fd2fd-onoff;realtime-dummy-network;fd2fd-onoff;dummy-network;tap-device-creator;raw-sock-creator;basic-energy-model-test;rv-battery-model-test;li-ion-energy-source;dsr;dsdv-manet;csma-star;csma-ping;csma-raw-ip-socket;csma-multicast;csma-packet-socket;csma-broadcast;csma-one-subnet;empirical-random-variable-example;main-test-sync;main-random-variable-stream;test-string-value-formatting;system-path-examples;sample-simulator;sample-show-progress;sample-random-variable-stream;sample-random-variable;sample-log-time-format;main-ptr;main-callback;length-example;hash-example;fatal-example;command-line-example;config-store-save;outdoor-random-walk-example;outdoor-group-mobility-example;buildings-pathloss-profiler;csma-bridge-one-hop;csma-bridge;three-gpp-http-example;aodv;stdlib_pch_exec;
//ADVANCED property for variable: ns3-external-libs
ns3-external-libs-ADVANCED:INTERNAL=1
//list of non-ns libraries to link to NS3_STATIC and NS3_MONOLIB
ns3-external-libs:INTERNAL=;/usr/lib/x86_64-linux-gnu/libgsl.so;/usr/lib/x86_64-linux-gnu/libgslcblas.so;;;;;;;/usr/lib/x86_64-linux-gnu/libsqlite3.so;;;;;;;;;;;;;;;;;;;;;;;/usr/lib/x86_64-linux-gnu/libgsl.so;/usr/lib/x86_64-linux-gnu/libgslcblas.so;/usr/lib/x86_64-linux-gnu/libxml2.so;/usr/lib/x86_64-linux-gnu/libfreetype.so;/usr/lib/x86_64-linux-gnu/libglib-2.0.so;/usr/lib/x86_64-linux-gnu/libgthread-2.0.so;/usr/lib/x86_64-linux-gnu/libgobject-2.0.so;/usr/lib/x86_64-linux-gnu/libgio-2.0.so;/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so;/usr/lib/x86_64-linux-gnu/libgdk-3.so;/usr/lib/x86_64-linux-gnu/libgtk-3.so;/usr/lib/x86_64-linux-gnu/libcairo.so;/usr/lib/x86_64-linux-gnu/libpango-1.0.so;/usr/lib/x86_64-linux-gnu/libatk-1.0.so;;;;;;
//ADVANCED property for variable: ns3-libs
ns3-libs-ADVANCED:INTERNAL=1
//list of processed upstream modules
ns3-libs:INTERNAL=libwimax;libwifi;libwave;libvirtual-net-device;libuan;libtraffic-control;libtopology-read;libtap-bridge;libstats;libspectrum;libsixlowpan;libpropagation;libpoint-to-point-layout;libpoint-to-point;libolsr;libnix-vector-routing;libnetwork;libnetanim;libmobility;libmesh;liblte;liblr-wpan;libinternet-apps;libinternet;libflow-monitor;libfd-net-device;libenergy;libdsr;libdsdv;libcsma-layout;libcsma;libcore;libconfig-store;libbuildings;libbridge;libapplications;libaodv;libantenna;
//ADVANCED property for variable: ns3-libs-tests
ns3-libs-tests-ADVANCED:INTERNAL=1
//list of test libraries
ns3-libs-tests:INTERNAL=libwimax-test;libwifi-test;libwave-test;libuan-test;libtraffic-control-test;libtopology-read-test;libstats-test;libspectrum-test;libsixlowpan-test;libpropagation-test;libpoint-to-point-test;libolsr-test;libnix-vector-routing-test;libnetwork-test;libnetanim-test;libmobility-test;libmesh-test;liblte-test;liblr-wpan-test;libinternet-apps-test;libinternet-test;libenergy-test;libdsr-test;libdsdv-test;libcore-test;libbuildings-test;libapplications-test;libaodv-test;libantenna-test;
//ADVANCED property for variable: openflow_header_internal_openflow.h
openflow_header_internal_openflow.h-ADVANCED:INTERNAL=1
//ADVANCED property for variable: openflow_library_internal_openflow
openflow_library_internal_openflow-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gsl
pkgcfg_lib_GSL_gsl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gslcblas
pkgcfg_lib_GSL_gslcblas-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_m
pkgcfg_lib_GSL_m-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_HARFBUZZ_harfbuzz
pkgcfg_lib_PC_HARFBUZZ_harfbuzz-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_LIBXML_xml2
pkgcfg_lib_PC_LIBXML_xml2-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu
testantenna:INTERNAL=libantenna-test
testaodv:INTERNAL=libaodv-test
testapplications:INTERNAL=libapplications-test
testbuildings:INTERNAL=libbuildings-test
testcore:INTERNAL=libcore-test
testdsdv:INTERNAL=libdsdv-test
testdsr:INTERNAL=libdsr-test
testenergy:INTERNAL=libenergy-test
testinternet:INTERNAL=libinternet-test
testinternet-apps:INTERNAL=libinternet-apps-test
testlr-wpan:INTERNAL=liblr-wpan-test
testlte:INTERNAL=liblte-test
testmesh:INTERNAL=libmesh-test
testmobility:INTERNAL=libmobility-test
testnetanim:INTERNAL=libnetanim-test
testnetwork:INTERNAL=libnetwork-test
testnix-vector-routing:INTERNAL=libnix-vector-routing-test
testolsr:INTERNAL=libolsr-test
testpoint-to-point:INTERNAL=libpoint-to-point-test
testpropagation:INTERNAL=libpropagation-test
testsixlowpan:INTERNAL=libsixlowpan-test
testspectrum:INTERNAL=libspectrum-test
teststats:INTERNAL=libstats-test
testtopology-read:INTERNAL=libtopology-read-test
testtraffic-control:INTERNAL=libtraffic-control-test
testuan:INTERNAL=libuan-test
testwave:INTERNAL=libwave-test
testwifi:INTERNAL=libwifi-test
testwimax:INTERNAL=libwimax-test

