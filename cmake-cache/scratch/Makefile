# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ns3/ns-allinone-3.37/ns-3.37

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/scratch//CMakeFiles/progress.marks
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(CMAKE_COMMAND) -P /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
scratch/CMakeFiles/scratch_apnotuse-kehu.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_apnotuse-kehu.dir/rule
.PHONY : scratch/CMakeFiles/scratch_apnotuse-kehu.dir/rule

# Convenience name for target.
scratch_apnotuse-kehu: scratch/CMakeFiles/scratch_apnotuse-kehu.dir/rule
.PHONY : scratch_apnotuse-kehu

# fast build rule for target.
scratch_apnotuse-kehu/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse-kehu.dir/build.make scratch/CMakeFiles/scratch_apnotuse-kehu.dir/build
.PHONY : scratch_apnotuse-kehu/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_apnotuse.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_apnotuse.dir/rule
.PHONY : scratch/CMakeFiles/scratch_apnotuse.dir/rule

# Convenience name for target.
scratch_apnotuse: scratch/CMakeFiles/scratch_apnotuse.dir/rule
.PHONY : scratch_apnotuse

# fast build rule for target.
scratch_apnotuse/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse.dir/build.make scratch/CMakeFiles/scratch_apnotuse.dir/build
.PHONY : scratch_apnotuse/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_bless.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_bless.dir/rule
.PHONY : scratch/CMakeFiles/scratch_bless.dir/rule

# Convenience name for target.
scratch_bless: scratch/CMakeFiles/scratch_bless.dir/rule
.PHONY : scratch_bless

# fast build rule for target.
scratch_bless/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_bless.dir/build.make scratch/CMakeFiles/scratch_bless.dir/build
.PHONY : scratch_bless/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_first.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_first.dir/rule
.PHONY : scratch/CMakeFiles/scratch_first.dir/rule

# Convenience name for target.
scratch_first: scratch/CMakeFiles/scratch_first.dir/rule
.PHONY : scratch_first

# fast build rule for target.
scratch_first/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_first.dir/build.make scratch/CMakeFiles/scratch_first.dir/build
.PHONY : scratch_first/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_icmp.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_icmp.dir/rule
.PHONY : scratch/CMakeFiles/scratch_icmp.dir/rule

# Convenience name for target.
scratch_icmp: scratch/CMakeFiles/scratch_icmp.dir/rule
.PHONY : scratch_icmp

# fast build rule for target.
scratch_icmp/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp.dir/build.make scratch/CMakeFiles/scratch_icmp.dir/build
.PHONY : scratch_icmp/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_icmp1.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_icmp1.dir/rule
.PHONY : scratch/CMakeFiles/scratch_icmp1.dir/rule

# Convenience name for target.
scratch_icmp1: scratch/CMakeFiles/scratch_icmp1.dir/rule
.PHONY : scratch_icmp1

# fast build rule for target.
scratch_icmp1/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp1.dir/build.make scratch/CMakeFiles/scratch_icmp1.dir/build
.PHONY : scratch_icmp1/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_scratch-simulator.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_scratch-simulator.dir/rule
.PHONY : scratch/CMakeFiles/scratch_scratch-simulator.dir/rule

# Convenience name for target.
scratch_scratch-simulator: scratch/CMakeFiles/scratch_scratch-simulator.dir/rule
.PHONY : scratch_scratch-simulator

# fast build rule for target.
scratch_scratch-simulator/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/build
.PHONY : scratch_scratch-simulator/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_simple_test.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_simple_test.dir/rule
.PHONY : scratch/CMakeFiles/scratch_simple_test.dir/rule

# Convenience name for target.
scratch_simple_test: scratch/CMakeFiles/scratch_simple_test.dir/rule
.PHONY : scratch_simple_test

# fast build rule for target.
scratch_simple_test/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_simple_test.dir/build.make scratch/CMakeFiles/scratch_simple_test.dir/build
.PHONY : scratch_simple_test/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_uan-test.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_uan-test.dir/rule
.PHONY : scratch/CMakeFiles/scratch_uan-test.dir/rule

# Convenience name for target.
scratch_uan-test: scratch/CMakeFiles/scratch_uan-test.dir/rule
.PHONY : scratch_uan-test

# fast build rule for target.
scratch_uan-test/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uan-test.dir/build.make scratch/CMakeFiles/scratch_uan-test.dir/build
.PHONY : scratch_uan-test/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_uav_3d.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_uav_3d.dir/rule
.PHONY : scratch/CMakeFiles/scratch_uav_3d.dir/rule

# Convenience name for target.
scratch_uav_3d: scratch/CMakeFiles/scratch_uav_3d.dir/rule
.PHONY : scratch_uav_3d

# fast build rule for target.
scratch_uav_3d/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uav_3d.dir/build.make scratch/CMakeFiles/scratch_uav_3d.dir/build
.PHONY : scratch_uav_3d/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule
.PHONY : scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule

# Convenience name for target.
scratch_underwater-relay-simulation: scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/rule
.PHONY : scratch_underwater-relay-simulation

# fast build rule for target.
scratch_underwater-relay-simulation/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build
.PHONY : scratch_underwater-relay-simulation/fast

# Convenience name for target.
scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule
.PHONY : scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule

# Convenience name for target.
scratch_subdir_scratch-subdir: scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/rule
.PHONY : scratch_subdir_scratch-subdir

# fast build rule for target.
scratch_subdir_scratch-subdir/fast:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build
.PHONY : scratch_subdir_scratch-subdir/fast

apnotuse-kehu.o: apnotuse-kehu.cc.o
.PHONY : apnotuse-kehu.o

# target to build an object file
apnotuse-kehu.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse-kehu.dir/build.make scratch/CMakeFiles/scratch_apnotuse-kehu.dir/apnotuse-kehu.cc.o
.PHONY : apnotuse-kehu.cc.o

apnotuse-kehu.i: apnotuse-kehu.cc.i
.PHONY : apnotuse-kehu.i

# target to preprocess a source file
apnotuse-kehu.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse-kehu.dir/build.make scratch/CMakeFiles/scratch_apnotuse-kehu.dir/apnotuse-kehu.cc.i
.PHONY : apnotuse-kehu.cc.i

apnotuse-kehu.s: apnotuse-kehu.cc.s
.PHONY : apnotuse-kehu.s

# target to generate assembly for a file
apnotuse-kehu.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse-kehu.dir/build.make scratch/CMakeFiles/scratch_apnotuse-kehu.dir/apnotuse-kehu.cc.s
.PHONY : apnotuse-kehu.cc.s

apnotuse.o: apnotuse.cc.o
.PHONY : apnotuse.o

# target to build an object file
apnotuse.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse.dir/build.make scratch/CMakeFiles/scratch_apnotuse.dir/apnotuse.cc.o
.PHONY : apnotuse.cc.o

apnotuse.i: apnotuse.cc.i
.PHONY : apnotuse.i

# target to preprocess a source file
apnotuse.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse.dir/build.make scratch/CMakeFiles/scratch_apnotuse.dir/apnotuse.cc.i
.PHONY : apnotuse.cc.i

apnotuse.s: apnotuse.cc.s
.PHONY : apnotuse.s

# target to generate assembly for a file
apnotuse.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_apnotuse.dir/build.make scratch/CMakeFiles/scratch_apnotuse.dir/apnotuse.cc.s
.PHONY : apnotuse.cc.s

bless.o: bless.cc.o
.PHONY : bless.o

# target to build an object file
bless.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_bless.dir/build.make scratch/CMakeFiles/scratch_bless.dir/bless.cc.o
.PHONY : bless.cc.o

bless.i: bless.cc.i
.PHONY : bless.i

# target to preprocess a source file
bless.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_bless.dir/build.make scratch/CMakeFiles/scratch_bless.dir/bless.cc.i
.PHONY : bless.cc.i

bless.s: bless.cc.s
.PHONY : bless.s

# target to generate assembly for a file
bless.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_bless.dir/build.make scratch/CMakeFiles/scratch_bless.dir/bless.cc.s
.PHONY : bless.cc.s

first.o: first.cc.o
.PHONY : first.o

# target to build an object file
first.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_first.dir/build.make scratch/CMakeFiles/scratch_first.dir/first.cc.o
.PHONY : first.cc.o

first.i: first.cc.i
.PHONY : first.i

# target to preprocess a source file
first.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_first.dir/build.make scratch/CMakeFiles/scratch_first.dir/first.cc.i
.PHONY : first.cc.i

first.s: first.cc.s
.PHONY : first.s

# target to generate assembly for a file
first.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_first.dir/build.make scratch/CMakeFiles/scratch_first.dir/first.cc.s
.PHONY : first.cc.s

icmp.o: icmp.cc.o
.PHONY : icmp.o

# target to build an object file
icmp.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp.dir/build.make scratch/CMakeFiles/scratch_icmp.dir/icmp.cc.o
.PHONY : icmp.cc.o

icmp.i: icmp.cc.i
.PHONY : icmp.i

# target to preprocess a source file
icmp.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp.dir/build.make scratch/CMakeFiles/scratch_icmp.dir/icmp.cc.i
.PHONY : icmp.cc.i

icmp.s: icmp.cc.s
.PHONY : icmp.s

# target to generate assembly for a file
icmp.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp.dir/build.make scratch/CMakeFiles/scratch_icmp.dir/icmp.cc.s
.PHONY : icmp.cc.s

icmp1.o: icmp1.cc.o
.PHONY : icmp1.o

# target to build an object file
icmp1.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp1.dir/build.make scratch/CMakeFiles/scratch_icmp1.dir/icmp1.cc.o
.PHONY : icmp1.cc.o

icmp1.i: icmp1.cc.i
.PHONY : icmp1.i

# target to preprocess a source file
icmp1.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp1.dir/build.make scratch/CMakeFiles/scratch_icmp1.dir/icmp1.cc.i
.PHONY : icmp1.cc.i

icmp1.s: icmp1.cc.s
.PHONY : icmp1.s

# target to generate assembly for a file
icmp1.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_icmp1.dir/build.make scratch/CMakeFiles/scratch_icmp1.dir/icmp1.cc.s
.PHONY : icmp1.cc.s

scratch-simulator.o: scratch-simulator.cc.o
.PHONY : scratch-simulator.o

# target to build an object file
scratch-simulator.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.o
.PHONY : scratch-simulator.cc.o

scratch-simulator.i: scratch-simulator.cc.i
.PHONY : scratch-simulator.i

# target to preprocess a source file
scratch-simulator.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.i
.PHONY : scratch-simulator.cc.i

scratch-simulator.s: scratch-simulator.cc.s
.PHONY : scratch-simulator.s

# target to generate assembly for a file
scratch-simulator.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_scratch-simulator.dir/build.make scratch/CMakeFiles/scratch_scratch-simulator.dir/scratch-simulator.cc.s
.PHONY : scratch-simulator.cc.s

simple_test.o: simple_test.cc.o
.PHONY : simple_test.o

# target to build an object file
simple_test.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_simple_test.dir/build.make scratch/CMakeFiles/scratch_simple_test.dir/simple_test.cc.o
.PHONY : simple_test.cc.o

simple_test.i: simple_test.cc.i
.PHONY : simple_test.i

# target to preprocess a source file
simple_test.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_simple_test.dir/build.make scratch/CMakeFiles/scratch_simple_test.dir/simple_test.cc.i
.PHONY : simple_test.cc.i

simple_test.s: simple_test.cc.s
.PHONY : simple_test.s

# target to generate assembly for a file
simple_test.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_simple_test.dir/build.make scratch/CMakeFiles/scratch_simple_test.dir/simple_test.cc.s
.PHONY : simple_test.cc.s

subdir/scratch-subdir-additional-header.o: subdir/scratch-subdir-additional-header.cc.o
.PHONY : subdir/scratch-subdir-additional-header.o

# target to build an object file
subdir/scratch-subdir-additional-header.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.o
.PHONY : subdir/scratch-subdir-additional-header.cc.o

subdir/scratch-subdir-additional-header.i: subdir/scratch-subdir-additional-header.cc.i
.PHONY : subdir/scratch-subdir-additional-header.i

# target to preprocess a source file
subdir/scratch-subdir-additional-header.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.i
.PHONY : subdir/scratch-subdir-additional-header.cc.i

subdir/scratch-subdir-additional-header.s: subdir/scratch-subdir-additional-header.cc.s
.PHONY : subdir/scratch-subdir-additional-header.s

# target to generate assembly for a file
subdir/scratch-subdir-additional-header.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir-additional-header.cc.s
.PHONY : subdir/scratch-subdir-additional-header.cc.s

subdir/scratch-subdir.o: subdir/scratch-subdir.cc.o
.PHONY : subdir/scratch-subdir.o

# target to build an object file
subdir/scratch-subdir.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.o
.PHONY : subdir/scratch-subdir.cc.o

subdir/scratch-subdir.i: subdir/scratch-subdir.cc.i
.PHONY : subdir/scratch-subdir.i

# target to preprocess a source file
subdir/scratch-subdir.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.i
.PHONY : subdir/scratch-subdir.cc.i

subdir/scratch-subdir.s: subdir/scratch-subdir.cc.s
.PHONY : subdir/scratch-subdir.s

# target to generate assembly for a file
subdir/scratch-subdir.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/build.make scratch/CMakeFiles/scratch_subdir_scratch-subdir.dir/subdir/scratch-subdir.cc.s
.PHONY : subdir/scratch-subdir.cc.s

uan-test.o: uan-test.cc.o
.PHONY : uan-test.o

# target to build an object file
uan-test.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uan-test.dir/build.make scratch/CMakeFiles/scratch_uan-test.dir/uan-test.cc.o
.PHONY : uan-test.cc.o

uan-test.i: uan-test.cc.i
.PHONY : uan-test.i

# target to preprocess a source file
uan-test.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uan-test.dir/build.make scratch/CMakeFiles/scratch_uan-test.dir/uan-test.cc.i
.PHONY : uan-test.cc.i

uan-test.s: uan-test.cc.s
.PHONY : uan-test.s

# target to generate assembly for a file
uan-test.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uan-test.dir/build.make scratch/CMakeFiles/scratch_uan-test.dir/uan-test.cc.s
.PHONY : uan-test.cc.s

uav_3d.o: uav_3d.cc.o
.PHONY : uav_3d.o

# target to build an object file
uav_3d.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uav_3d.dir/build.make scratch/CMakeFiles/scratch_uav_3d.dir/uav_3d.cc.o
.PHONY : uav_3d.cc.o

uav_3d.i: uav_3d.cc.i
.PHONY : uav_3d.i

# target to preprocess a source file
uav_3d.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uav_3d.dir/build.make scratch/CMakeFiles/scratch_uav_3d.dir/uav_3d.cc.i
.PHONY : uav_3d.cc.i

uav_3d.s: uav_3d.cc.s
.PHONY : uav_3d.s

# target to generate assembly for a file
uav_3d.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_uav_3d.dir/build.make scratch/CMakeFiles/scratch_uav_3d.dir/uav_3d.cc.s
.PHONY : uav_3d.cc.s

underwater-relay-simulation.o: underwater-relay-simulation.cc.o
.PHONY : underwater-relay-simulation.o

# target to build an object file
underwater-relay-simulation.cc.o:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.o
.PHONY : underwater-relay-simulation.cc.o

underwater-relay-simulation.i: underwater-relay-simulation.cc.i
.PHONY : underwater-relay-simulation.i

# target to preprocess a source file
underwater-relay-simulation.cc.i:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.i
.PHONY : underwater-relay-simulation.cc.i

underwater-relay-simulation.s: underwater-relay-simulation.cc.s
.PHONY : underwater-relay-simulation.s

# target to generate assembly for a file
underwater-relay-simulation.cc.s:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(MAKE) $(MAKESILENT) -f scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/build.make scratch/CMakeFiles/scratch_underwater-relay-simulation.dir/underwater-relay-simulation.cc.s
.PHONY : underwater-relay-simulation.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... scratch_apnotuse"
	@echo "... scratch_apnotuse-kehu"
	@echo "... scratch_bless"
	@echo "... scratch_first"
	@echo "... scratch_icmp"
	@echo "... scratch_icmp1"
	@echo "... scratch_scratch-simulator"
	@echo "... scratch_simple_test"
	@echo "... scratch_subdir_scratch-subdir"
	@echo "... scratch_uan-test"
	@echo "... scratch_uav_3d"
	@echo "... scratch_underwater-relay-simulation"
	@echo "... apnotuse-kehu.o"
	@echo "... apnotuse-kehu.i"
	@echo "... apnotuse-kehu.s"
	@echo "... apnotuse.o"
	@echo "... apnotuse.i"
	@echo "... apnotuse.s"
	@echo "... bless.o"
	@echo "... bless.i"
	@echo "... bless.s"
	@echo "... first.o"
	@echo "... first.i"
	@echo "... first.s"
	@echo "... icmp.o"
	@echo "... icmp.i"
	@echo "... icmp.s"
	@echo "... icmp1.o"
	@echo "... icmp1.i"
	@echo "... icmp1.s"
	@echo "... scratch-simulator.o"
	@echo "... scratch-simulator.i"
	@echo "... scratch-simulator.s"
	@echo "... simple_test.o"
	@echo "... simple_test.i"
	@echo "... simple_test.s"
	@echo "... subdir/scratch-subdir-additional-header.o"
	@echo "... subdir/scratch-subdir-additional-header.i"
	@echo "... subdir/scratch-subdir-additional-header.s"
	@echo "... subdir/scratch-subdir.o"
	@echo "... subdir/scratch-subdir.i"
	@echo "... subdir/scratch-subdir.s"
	@echo "... uan-test.o"
	@echo "... uan-test.i"
	@echo "... uan-test.s"
	@echo "... uav_3d.o"
	@echo "... uav_3d.i"
	@echo "... uav_3d.s"
	@echo "... underwater-relay-simulation.o"
	@echo "... underwater-relay-simulation.i"
	@echo "... underwater-relay-simulation.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(CMAKE_COMMAND) -P /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

