# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DHAVE_GSL -DHAVE_LIBXML2 -DHAVE_SQLITE3 -DNS3_ASSERT_ENABLE -DNS3_BUILD_PROFILE_DEBUG -DNS3_LOG_ENABLE -DPROJECT_SOURCE_PATH=\"/home/<USER>/ns3/ns-allinone-3.37/ns-3.37\" -D__LINUX__

CXX_INCLUDES = -I/usr -I/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/include -I/usr/include/freetype2 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/gtk-3.0 -I/usr/include/cairo -I/usr/include/pango-1.0 -I/usr/include/harfbuzz -I/usr/include/atk-1.0 -I/usr/include/libxml2 -I/usr/include/python3.10

CXX_FLAGS = -g -fPIE   -fno-semantic-interposition -fdiagnostics-color=always -Wall -Werror -Wno-error=deprecated-declarations -std=gnu++17

# PCH options: scratch/CMakeFiles/scratch_simple_test.dir/simple_test.cc.o_OPTIONS = -Winvalid-pch;-include;/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/stdlib_pch_exec.dir/cmake_pch.hxx

