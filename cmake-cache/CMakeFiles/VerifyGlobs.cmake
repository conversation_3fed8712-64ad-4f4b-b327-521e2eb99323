# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# single_source_file_scratches at scratch/CMakeLists.txt:57 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/[^.]*.cc")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/apnotuse-kehu.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/apnotuse.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/bless.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/first.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/icmp.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/icmp1.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/scratch-simulator.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/simple_test.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/tutorial-app.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uan-bouy-ship.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uan-test.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uav_3d.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/underwater-relay-simulation.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()

# scratch_sources at scratch/CMakeLists.txt:88 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/[^.]*.cc")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/scratch-subdir-additional-header.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/scratch-subdir.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()

# scratch_subdirectories at scratch/CMakeLists.txt:64 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES true "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/**")
set(OLD_GLOB
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/CMakeLists.txt"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/apnotuse-kehu.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/apnotuse.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/bless.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/first.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/icmp.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/icmp1.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/scratch-simulator.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/simple_test.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/scratch-subdir-additional-header.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/scratch-subdir-additional-header.h"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/subdir/scratch-subdir.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/tutorial-app.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uan-bouy-ship.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uan-test.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/uav_3d.cc"
  "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/scratch/underwater-relay-simulation.cc"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/CMakeFiles/cmake.verify_globs")
endif()
