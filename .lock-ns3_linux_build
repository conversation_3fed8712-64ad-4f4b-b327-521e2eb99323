#! /usr/bin/env python3

launch_dir = '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37'
run_dir = '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37'
top_dir = '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37'
out_dir = '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build'


NS3_ENABLED_MODULES = ['ns3-wimax', 'ns3-wifi', 'ns3-wave', 'ns3-virtual-net-device', 'ns3-uan', 'ns3-traffic-control', 'ns3-topology-read', 'ns3-tap-bridge', 'ns3-stats', 'ns3-spectrum', 'ns3-sixlowpan', 'ns3-propagation', 'ns3-point-to-point-layout', 'ns3-point-to-point', 'ns3-olsr', 'ns3-nix-vector-routing', 'ns3-network', 'ns3-netanim', 'ns3-mobility', 'ns3-mesh', 'ns3-lte', 'ns3-lr-wpan', 'ns3-internet-apps', 'ns3-internet', 'ns3-flow-monitor', 'ns3-fd-net-device', 'ns3-energy', 'ns3-dsr', 'ns3-dsdv', 'ns3-csma-layout', 'ns3-csma', 'ns3-core', 'ns3-config-store', 'ns3-buildings', 'ns3-bridge', 'ns3-applications', 'ns3-aodv', 'ns3-antenna', ]
NS3_ENABLED_CONTRIBUTED_MODULES = []
NS3_MODULE_PATH = ['/home/<USER>/.vscode-server/cli/servers/Stable-488a1f239235055e34e673291fb8d8c810886f81/server/bin/remote-cli', '/home/<USER>/.local/bin', '/usr/local/sbin', '/usr/local/bin', '/usr/sbin', '/usr/bin', '/sbin', '/bin', '/usr/games', '/usr/local/games', '/snap/bin', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/lib']
ENABLE_REAL_TIME = False
ENABLE_EXAMPLES = True
ENABLE_TESTS = True
ENABLE_OPENFLOW = False
NSCLICK = False
ENABLE_BRITE = False
ENABLE_SUDO = False
ENABLE_PYTHON_BINDINGS = False
EXAMPLE_DIRECTORIES = ['wireless', 'udp-client-server', 'udp', 'tutorial', 'traffic-control', 'tcp', 'stats', 'socket', 'routing', 'realtime', 'naming', 'matrix-topology', 'ipv6', 'error-model', 'energy', 'channel-models', ]
APPNAME = 'ns'
BUILD_PROFILE = 'debug'
VERSION = '3.37' 
BUILD_VERSION_STRING = '' 
PYTHON = ['/usr/bin/python3.10']
VALGRIND_FOUND = True 


ns3_runnable_programs = ['/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/perf/ns3.37-perf-io-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-print-introspected-doxygen-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-bench-packets-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-bench-scheduler-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/utils/ns3.37-test-runner-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/subdir/ns3.37-scratch-subdir-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-underwater-relay-simulation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-uav_3d-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-uan-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-simple_test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-scratch-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-icmp1-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-icmp-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-first-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-bless-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-apnotuse-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/scratch/ns3.37-apnotuse-kehu-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-eht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-wired-bridging-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-vht-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-txop-aggregation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-timing-attributes-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-tcp-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-saturation-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-per-interference-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spectrum-per-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-spatial-reuse-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-sleep-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-interference-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-infra-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-ht-hidden-stations-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-adhoc-grid-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-simple-adhoc-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-rate-adaptation-distance-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-power-adaptation-interference-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-power-adaptation-distance-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-error-models-comparison-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-vht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-ht-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ofdm-he-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-multirate-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-multi-tos-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-mixed-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ht-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-hidden-terminal-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-he-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-dsss-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-clear-channel-cmu-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-blockack-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-backward-compatibility-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-ap-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-aggregation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-adhoc-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-80211n-mimo-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-wifi-80211e-txop-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/wireless/ns3.37-mixed-wired-wireless-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp-client-server/ns3.37-udp-trace-client-server-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp-client-server/ns3.37-udp-client-server-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/udp/ns3.37-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-seventh-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-sixth-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-fifth-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-fourth-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-third-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-second-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-first-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tutorial/ns3.37-hello-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-cobalt-vs-codel-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-tbf-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-red-vs-nlred-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-red-vs-fengadaptive-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-queue-discs-benchmark-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/traffic-control/ns3.37-traffic-control-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-dctcp-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-validation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-linux-reno-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-pacing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-variants-comparison-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-pcap-nanosec-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-bulk-send-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-bbr-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-star-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-star-server-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/tcp/ns3.37-tcp-large-transfer-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/stats/ns3.37-wifi-example-sim-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-options-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-options-ipv4-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-bound-tcp-static-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/socket/ns3.37-socket-bound-static-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-multicast-flooding-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-routing-multi-switch-plus-router-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-rip-simple-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-ripng-simple-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-manet-routing-compare-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-routing-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-mixed-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-alternate-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-simple-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-injection-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-global-routing-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-static-routing-slash32-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/routing/ns3.37-dynamic-global-routing-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/realtime/ns3.37-realtime-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/naming/ns3.37-object-names-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/matrix-topology/ns3.37-matrix-topology-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-PMTU-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-wsn-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-test-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-radvd-two-prefix-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-radvd-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-loose-routing-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-icmpv6-redirect-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-two-MTU-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/ipv6/ns3.37-fragmentation-ipv6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/error-model/ns3.37-simple-error-model-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/energy/ns3.37-energy-model-with-harvesting-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/energy/ns3.37-energy-model-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/examples/channel-models/ns3.37-three-gpp-v2v-channel-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-simple-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-multicast-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wimax/examples/ns3.37-wimax-ipv4-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-bianchi-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-phy-configuration-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-trans-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-manager-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-test-interference-helper-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wifi/examples/ns3.37-wifi-phy-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-vanet-routing-compare-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-wave-simple-device-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/wave/examples/ns3.37-wave-simple-80211p-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/virtual-net-device/examples/ns3.37-virtual-net-device-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-6lowpan-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-raw-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-ipv6-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-ipv4-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-rc-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/uan/examples/ns3.37-uan-cw-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-fqcodel-l4s-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-pie-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-codel-vs-pfifo-asymmetric-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-codel-vs-pfifo-basic-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-pfifo-vs-red-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-adaptive-red-tests-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-red-vs-ared-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/traffic-control/examples/ns3.37-red-tests-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/topology-read/examples/ns3.37-topology-example-sim-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/ns3.37-tap-creator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-wifi-dumbbell-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-wifi-virtual-machine-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-csma-virtual-machine-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/tap-bridge/examples/ns3.37-tap-csma-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-file-helper-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-file-aggregator-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-helper-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-aggregator-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-double-probe-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-gnuplot-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/stats/examples/ns3.37-time-probe-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-three-gpp-channel-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-tv-trans-regional-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-tv-trans-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-with-microwave-oven-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-matrix-propagation-loss-model-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/spectrum/examples/ns3.37-adhoc-aloha-ideal-phy-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-mesh-under-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-beacon-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-ping-lr-wpan-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/sixlowpan/examples/ns3.37-example-sixlowpan-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/propagation/examples/ns3.37-jakes-propagation-model-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/propagation/examples/ns3.37-main-propagation-loss-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/point-to-point/examples/ns3.37-main-attribute-value-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/olsr/examples/ns3.37-simple-point-to-point-olsr-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/olsr/examples/ns3.37-olsr-hna-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-double-wifi-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nms-p2p-nix-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-simple-multi-address-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/nix-vector-routing/examples/ns3.37-nix-simple-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-lollipop-comparisions-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-packet-socket-apps-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-main-packet-tag-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-main-packet-header-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/network/examples/ns3.37-bit-serializer-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-uan-animation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-wireless-animation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-resources-counters-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-colors-link-description-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-star-animation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-grid-animation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/netanim/examples/ns3.37-dumbbell-animation-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-reference-point-group-mobility-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-mobility-trace-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-grid-topology-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-ns2-mobility-trace-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-random-walk-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-main-random-topology-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mobility/examples/ns3.37-bonnmotion-ns2-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/mesh/examples/ns3.37-mesh-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-emu-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-x2-handover-measures-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-x2-handover-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-uplink-power-control-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-backhaul-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-epc-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-simple-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rlc-traces-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rem-sector-antenna-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-rem-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-radio-link-failure-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-profiling-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-pathloss-traces-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-ue-ue-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-ue-rh-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-ipv6-addr-conf-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-intercell-interference-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-frequency-reuse-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-fading-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-dual-stripe-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-distributed-ffr-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-deactivate-bearer-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-cqi-threshold-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lte/examples/ns3.37-lena-cc-helper-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-bootstrap-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-error-model-plot-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-error-distance-plot-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-active-scan-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-ed-scan-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-phy-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-packet-print-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-mlme-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/lr-wpan/examples/ns3.37-lr-wpan-data-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet-apps/examples/ns3.37-traceroute-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet-apps/examples/ns3.37-dhcp-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-neighbor-cache-dynamic-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-neighbor-cache-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/internet/examples/ns3.37-main-simple-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-tap-ping6-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-tap-ping-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-tc-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-send-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-udp-echo-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd-emu-ping-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-realtime-fd2fd-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-realtime-dummy-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-fd2fd-onoff-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/examples/ns3.37-dummy-network-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/ns3.37-tap-device-creator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/fd-net-device/ns3.37-raw-sock-creator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-basic-energy-model-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-rv-battery-model-test-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/energy/examples/ns3.37-li-ion-energy-source-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/dsr/examples/ns3.37-dsr-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/dsdv/examples/ns3.37-dsdv-manet-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma-layout/examples/ns3.37-csma-star-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-ping-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-raw-ip-socket-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-multicast-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-packet-socket-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-broadcast-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/csma/examples/ns3.37-csma-one-subnet-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-empirical-random-variable-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-test-sync-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-random-variable-stream-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-test-string-value-formatting-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-system-path-examples-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-simulator-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-show-progress-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-random-variable-stream-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-random-variable-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-sample-log-time-format-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-ptr-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-main-callback-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-length-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-hash-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-fatal-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/core/examples/ns3.37-command-line-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/config-store/examples/ns3.37-config-store-save-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-outdoor-random-walk-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-outdoor-group-mobility-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/buildings/examples/ns3.37-buildings-pathloss-profiler-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/bridge/examples/ns3.37-csma-bridge-one-hop-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/bridge/examples/ns3.37-csma-bridge-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/applications/examples/ns3.37-three-gpp-http-example-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/build/src/aodv/examples/ns3.37-aodv-debug', '/home/<USER>/ns3/ns-allinone-3.37/ns-3.37/cmake-cache/ns3.37-stdlib_pch_exec-debug', ]

ns3_runnable_scripts = []

