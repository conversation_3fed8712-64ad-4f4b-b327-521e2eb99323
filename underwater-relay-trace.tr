+ 5 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::Uan<PERSON>eader<PERSON>ommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 0 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=0 time=+5s)) Payload (size=20)
t 5.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 5.00301 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 5.00373 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 5.00373 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=0) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
t 5.00502 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 5.00702 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 5.00774 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 6.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00098 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00102 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 6.0017 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 7.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.0008 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 7.00098 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 7.0017 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 8.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.00086 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 8.00088 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 8.0016 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 11.3559 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=0) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
r 14.4918 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=0) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
r 14.4948 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=0) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
+ 14.52 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 1 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=1 time=+7s)) Payload (size=20)
r 20.8559 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 1 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=1 time=+7s)) Payload (size=20)
r 20.887 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 1 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=1 time=+7s)) Payload (size=20)
+ 20.907 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 0 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=0 time=+7.5s)) Payload (size=20)
+ 20.96 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 5 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=5 time=+15s)) Payload (size=20)
+ 27.354 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 8 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=8 time=+21s)) Payload (size=20)
r 33.6899 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 8 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=8 time=+21s)) Payload (size=20)
+ 33.694 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 12 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=12 time=+29s)) Payload (size=20)
r 33.721 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 8 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=8 time=+21s)) Payload (size=20)
+ 33.727 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 6 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=6 time=+22.5s)) Payload (size=20)
+ 40.101 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 11 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=11 time=+35s)) Payload (size=20)
r 46.4399 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 11 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=11 time=+35s)) Payload (size=20)
r 46.468 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 11 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=11 time=+35s)) Payload (size=20)
+ 46.494 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 15 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=15 time=+35s)) Payload (size=20)
+ 46.541 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 14 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=14 time=+42.5s)) Payload (size=20)
+ 52.881 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 16 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=16 time=+47.5s)) Payload (size=20)
r 59.2199 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 16 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=16 time=+47.5s)) Payload (size=20)
r 59.248 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 16 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=16 time=+47.5s)) Payload (size=20)
+ 59.341 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 19 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=19 time=+55s)) Payload (size=20)
+ 59.388 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 21 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=21 time=+47s)) Payload (size=20)
+ 65.795 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 21 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=21 time=+60s)) Payload (size=20)
+ 65.828 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 28 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=28 time=+61s)) Payload (size=20)
+ 72.222 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 31 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=31 time=+67s)) Payload (size=20)
+ 72.275 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 24 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=24 time=+67.5s)) Payload (size=20)
+ 78.749 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 26 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=26 time=+72.5s)) Payload (size=20)
+ 78.762 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 34 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=34 time=+73s)) Payload (size=20)
+ 85.129 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 29 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=29 time=+80s)) Payload (size=20)
+ 85.196 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 37 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=37 time=+79s)) Payload (size=20)
+ 91.616 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 41 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=41 time=+87s)) Payload (size=20)
r 97.9519 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 41 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=41 time=+87s)) Payload (size=20)
+ 97.976 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 44 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=44 time=+93s)) Payload (size=20)
r 97.983 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 41 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=41 time=+87s)) Payload (size=20)
+ 98.003 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 32 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=32 time=+87.5s)) Payload (size=20)
+ 104.343 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 37 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=37 time=+100s)) Payload (size=20)
t 110.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 110.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 110.006 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 110.007 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 110.682 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 37 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=37 time=+100s)) Payload (size=20)
+ 110.683 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 39 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=39 time=+105s)) Payload (size=20)
r 110.71 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 37 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=37 time=+100s)) Payload (size=20)
t 111 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 111.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 111.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 111.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 111.005 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 112 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 112 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 112.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 112.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 112.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 113 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 113.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 113.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113.003 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 114 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 114.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 115 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 117.022 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 39 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=39 time=+105s)) Payload (size=20)
r 117.05 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 39 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=39 time=+105s)) Payload (size=20)
+ 117.082 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=0) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
+ 117.09 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 47 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=47 time=+99s)) Payload (size=20)
+ 123.45 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 57 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=57 time=+119s)) Payload (size=20)
+ 123.479 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 42 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=42 time=+112.5s)) Payload (size=20)
+ 129.906 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 60 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=60 time=+125s)) Payload (size=20)
+ 129.937 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 47 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=47 time=+125s)) Payload (size=20)
+ 136.324 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 63 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=63 time=+131s)) Payload (size=20)
r 142.66 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 63 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=63 time=+131s)) Payload (size=20)
r 142.691 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 63 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=63 time=+131s)) Payload (size=20)
+ 142.744 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 66 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=66 time=+137s)) Payload (size=20)
+ 142.753 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 49 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=49 time=+130s)) Payload (size=20)
+ 149.111 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 55 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=55 time=+145s)) Payload (size=20)
r 155.45 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 55 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=55 time=+145s)) Payload (size=20)
r 155.478 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 55 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=55 time=+145s)) Payload (size=20)
+ 155.56 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 69 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=69 time=+143s)) Payload (size=20)
+ 155.571 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 57 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=57 time=+150s)) Payload (size=20)
+ 162.107 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 60 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=60 time=+157.5s)) Payload (size=20)
+ 162.118 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 76 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=76 time=+157s)) Payload (size=20)
+ 168.634 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 79 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=79 time=+163s)) Payload (size=20)
+ 168.665 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 62 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=62 time=+162.5s)) Payload (size=20)
+ 175.152 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 82 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=82 time=+169s)) Payload (size=20)
+ 175.161 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 65 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=65 time=+170s)) Payload (size=20)
+ 181.528 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 86 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=86 time=+177s)) Payload (size=20)
+ 181.559 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 68 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=68 time=+177.5s)) Payload (size=20)
+ 187.946 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 89 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=89 time=+183s)) Payload (size=20)
r 194.282 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 89 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=89 time=+183s)) Payload (size=20)
+ 194.286 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 92 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=92 time=+189s)) Payload (size=20)
r 194.313 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/RxOk ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 89 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9080) ns3::SeqTsHeader ((seq=89 time=+183s)) Payload (size=20)
+ 194.335 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=0) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 70 protocol 17 offset (bytes) 0 flags [none] length: 60 ******** > ********) ns3::UdpHeader (length: 40 49153 > 9081) ns3::SeqTsHeader ((seq=70 time=+182.5s)) Payload (size=20)
