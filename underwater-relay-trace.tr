+ 5 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::Uan<PERSON>eaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 0 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=0 time=+5s)) Payload (size=52)
t 5.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 5.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 5.00073 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=2) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
t 5.00801 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 5.00802 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 5.00874 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 5.00902 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 6.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 6.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00082 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 6.00102 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 6.00154 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 6.2 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 0 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=0 time=+6.2s)) Payload (size=52)
t 7.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 7.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.0009 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 7.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 7.00162 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.00001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.00001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 8.00073 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.0009 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 8.0009 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
r 8.00162 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 15 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 10 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=10 time=+15s)) Payload (size=52)
+ 15.8 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 8 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=8 time=+15.8s)) Payload (size=52)
+ 25 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 20 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=20 time=+25s)) Payload (size=52)
+ 25.4 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 16 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=16 time=+25.4s)) Payload (size=52)
+ 35 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 24 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=24 time=+35s)) Payload (size=52)
+ 35 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 30 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=30 time=+35s)) Payload (size=52)
+ 44.6 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 32 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=32 time=+44.6s)) Payload (size=52)
+ 45 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 40 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=40 time=+45s)) Payload (size=52)
+ 54.2 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 40 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=40 time=+54.2s)) Payload (size=52)
+ 55 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 50 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=50 time=+55s)) Payload (size=52)
+ 63.8 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 48 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=48 time=+63.8s)) Payload (size=52)
+ 65 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 60 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=60 time=+65s)) Payload (size=52)
+ 73.4 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 56 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=56 time=+73.4s)) Payload (size=52)
+ 75 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 70 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=70 time=+75s)) Payload (size=52)
+ 83 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 64 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=64 time=+83s)) Payload (size=52)
+ 85 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 80 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=80 time=+85s)) Payload (size=52)
+ 92.6 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 72 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=72 time=+92.6s)) Payload (size=52)
+ 95 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 90 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=90 time=+95s)) Payload (size=52)
+ 102.2 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 80 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=80 time=+102.2s)) Payload (size=52)
+ 105 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 100 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=100 time=+105s)) Payload (size=52)
t 110.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 110.005 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 110.006 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 110.006 /NodeList/2/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=2 dest=255 type=0Protocol Number=2) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********)
t 111 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 111 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 111.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 111.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 111.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 111.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 111.003 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 111.004 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 111.006 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
+ 111.8 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 88 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=88 time=+111.8s)) Payload (size=52)
t 112 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 112 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 112.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 112.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 112.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 112.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 113 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 113.001 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
t 113.001 /NodeList/4/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:03 source ipv4: ******** dest ipv4: ********) 
t 113.001 /NodeList/3/DeviceList/0/$ns3::WifiNetDevice/Phy/State/Tx DsssRate1Mbps ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
r 113.002 DsssRate1Mbps /NodeList/2/DeviceList/1/$ns3::WifiNetDevice/Phy/State/RxOk ns3::WifiMacHeader (QOSDATA ) ns3::LlcSnapHeader (type 0x806) ns3::ArpHeader (request source mac: 00-06-00:00:00:00:00:02 source ipv4: ******** dest ipv4: ********) 
+ 115 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 110 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=110 time=+115s)) Payload (size=52)
+ 121.4 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 96 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=96 time=+121.4s)) Payload (size=52)
+ 125 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 120 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=120 time=+125s)) Payload (size=52)
+ 131 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 104 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=104 time=+131s)) Payload (size=52)
+ 135 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 130 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=130 time=+135s)) Payload (size=52)
+ 140.6 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 112 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=112 time=+140.6s)) Payload (size=52)
+ 145 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 140 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=140 time=+145s)) Payload (size=52)
+ 150.2 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 120 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=120 time=+150.2s)) Payload (size=52)
+ 155 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 150 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=150 time=+155s)) Payload (size=52)
+ 159.8 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 128 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=128 time=+159.8s)) Payload (size=52)
+ 165 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 160 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=160 time=+165s)) Payload (size=52)
+ 169.4 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 136 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=136 time=+169.4s)) Payload (size=52)
+ 175 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 170 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=170 time=+175s)) Payload (size=52)
+ 179 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 144 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=144 time=+179s)) Payload (size=52)
+ 185 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 180 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=180 time=+185s)) Payload (size=52)
+ 188.6 /NodeList/1/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=1 dest=0 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 152 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9081) ns3::SeqTsHeader ((seq=152 time=+188.6s)) Payload (size=52)
+ 195 /NodeList/0/DeviceList/0/$ns3::UanNetDevice/Phy/Tx ns3::UanHeaderCommon (UAN src=0 dest=1 type=0Protocol Number=1) ns3::Ipv4Header (tos 0x0 DSCP Default ECN Not-ECT ttl 64 id 190 protocol 17 offset (bytes) 0 flags [none] length: 92 ******** > ********) ns3::UdpHeader (length: 72 49153 > 9080) ns3::SeqTsHeader ((seq=190 time=+195s)) Payload (size=52)
