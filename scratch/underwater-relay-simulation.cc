/*
 * 水下-水面-空中通信仿真
 *
 * 此仿真演示了一个多层通信系统：
 * - 水下层：UAN（水声网络）设备
 * - 水面层：具有UAN和WiFi双接口的浮动中继节点
 * - 水面上层：具有WiFi接口的船舶和无人机节点
 *
 * 通信路径：UAN ↔ 中继 ↔ WiFi（船舶/无人机）
 * 所有通信都必须通过中继节点进行。
 */

#include "ns3/core-module.h"
#include "ns3/network-module.h"
#include "ns3/internet-module.h"
#include "ns3/internet-apps-module.h"
#include "ns3/mobility-module.h"
#include "ns3/applications-module.h"
#include "ns3/uan-module.h"
#include "ns3/wifi-module.h"
#include "ns3/bridge-module.h"
#include "ns3/csma-module.h"
#include "ns3/point-to-point-module.h"
#include "ns3/netanim-module.h"
#include "ns3/flow-monitor-module.h"
#include <iomanip>

using namespace ns3;

NS_LOG_COMPONENT_DEFINE("UnderwaterRelaySimulation");

// 用于跟踪数据包的全局变量
uint32_t g_uanTxPackets = 0;
uint32_t g_uanRxPackets = 0;
uint32_t g_wifiTxPackets = 0;
uint32_t g_wifiRxPackets = 0;
uint32_t g_relayForwardedPackets = 0;

// 每个节点的数据包计数器
uint32_t g_uanNode0TxPackets = 0;
uint32_t g_uanNode0RxPackets = 0;
uint32_t g_uanNode1TxPackets = 0;
uint32_t g_uanNode1RxPackets = 0;
uint32_t g_relayTxPackets = 0;
uint32_t g_relayRxPackets = 0;
uint32_t g_shipTxPackets = 0;
uint32_t g_shipRxPackets = 0;
uint32_t g_uavTxPackets = 0;
uint32_t g_uavRxPackets = 0;

// 数据包跟踪回调函数
void UanTxTrace(Ptr<const Packet> packet)
{
    g_uanTxPackets++;
    NS_LOG_INFO("水声发送: 数据包 " << packet->GetUid() << " 在 " << Simulator::Now().GetSeconds() << "秒 发送");
}

void UanRxTrace(Ptr<const Packet> packet)
{
    g_uanRxPackets++;
    NS_LOG_INFO("水声接收: 数据包 " << packet->GetUid() << " 在 " << Simulator::Now().GetSeconds() << "秒 接收");
}

void WifiTxTrace(Ptr<const Packet> packet)
{
    g_wifiTxPackets++;
    NS_LOG_INFO("WiFi发送: 数据包 " << packet->GetUid() << " 在 " << Simulator::Now().GetSeconds() << "秒 发送");
}

void WifiRxTrace(Ptr<const Packet> packet)
{
    g_wifiRxPackets++;
    NS_LOG_INFO("WiFi接收: 数据包 " << packet->GetUid() << " 在 " << Simulator::Now().GetSeconds() << "秒 接收");
}

// 自定义中继应用程序类
class RelayApplication : public Application
{
public:
    static TypeId GetTypeId();
    RelayApplication();
    virtual ~RelayApplication();

    void SetUanDevice(Ptr<NetDevice> uanDevice);  // 设置UAN设备
    void SetWifiDevice(Ptr<NetDevice> wifiDevice);  // 设置WiFi设备

protected:
    virtual void DoDispose();

private:
    virtual void StartApplication();  // 启动应用程序
    virtual void StopApplication();   // 停止应用程序

    // 处理从UAN网络接收的数据包
    void HandleUanReceive(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from);
    // 处理从WiFi网络接收的数据包
    void HandleWifiReceive(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from);

    Ptr<NetDevice> m_uanDevice;   // UAN网络设备指针
    Ptr<NetDevice> m_wifiDevice;  // WiFi网络设备指针
};

TypeId
RelayApplication::GetTypeId()
{
    static TypeId tid = TypeId("RelayApplication")
                            .SetParent<Application>()
                            .SetGroupName("Applications")
                            .AddConstructor<RelayApplication>();
    return tid;
}

RelayApplication::RelayApplication()
{
    NS_LOG_FUNCTION(this);
}

RelayApplication::~RelayApplication()
{
    NS_LOG_FUNCTION(this);
}

void
RelayApplication::SetUanDevice(Ptr<NetDevice> uanDevice)
{
    m_uanDevice = uanDevice;
}

void
RelayApplication::SetWifiDevice(Ptr<NetDevice> wifiDevice)
{
    m_wifiDevice = wifiDevice;
}

void
RelayApplication::DoDispose()
{
    NS_LOG_FUNCTION(this);
    m_uanDevice = nullptr;
    m_wifiDevice = nullptr;
    Application::DoDispose();
}

void
RelayApplication::StartApplication()
{
    NS_LOG_FUNCTION(this);

    if (m_uanDevice)
    {
        m_uanDevice->SetReceiveCallback(MakeCallback(&RelayApplication::HandleUanReceive, this));
        NS_LOG_INFO("中继: UAN接收回调已设置");
    }

    if (m_wifiDevice)
    {
        m_wifiDevice->SetReceiveCallback(MakeCallback(&RelayApplication::HandleWifiReceive, this));
        NS_LOG_INFO("中继: WiFi接收回调已设置");
    }
}

void
RelayApplication::StopApplication()
{
    NS_LOG_FUNCTION(this);
}

void
RelayApplication::HandleUanReceive(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from)
{
    NS_LOG_INFO("中继: 从水声网络接收数据包 " << packet->GetUid() << " (大小: " << packet->GetSize() << "字节), 转发到WiFi网络");
    g_relayForwardedPackets++;

    // 将数据包以广播方式转发到WiFi网络
    if (m_wifiDevice)
    {
        Ptr<Packet> copy = packet->Copy();
        // 使用WiFi广播地址进行转发
        Mac48Address wifiBroadcast = Mac48Address::GetBroadcast();
        bool success = m_wifiDevice->Send(copy, wifiBroadcast, protocol);
        if (success)
        {
            NS_LOG_INFO("中继: 成功将数据包 " << packet->GetUid() << " 从水声网络转发到WiFi网络");
        }
        else
        {
            NS_LOG_WARN("中继: 转发数据包 " << packet->GetUid() << " 从水声网络到WiFi网络失败");
        }
    }
}

void
RelayApplication::HandleWifiReceive(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from)
{
    NS_LOG_INFO("中继: 从WiFi网络接收数据包 " << packet->GetUid() << " (大小: " << packet->GetSize() << "字节), 转发到水声网络");
    g_relayForwardedPackets++;

    // 将数据包以广播方式转发到UAN网络
    if (m_uanDevice)
    {
        Ptr<Packet> copy = packet->Copy();
        // 使用UAN广播地址进行转发
        Mac8Address uanBroadcast = Mac8Address::GetBroadcast();

        // 检查UAN设备状态
        Ptr<UanNetDevice> uanDev = DynamicCast<UanNetDevice>(m_uanDevice);
        if (uanDev && uanDev->GetMac())
        {
            // 尝试发送数据包
            bool success = m_uanDevice->Send(copy, uanBroadcast, protocol);
            if (success)
            {
                NS_LOG_INFO("中继: 成功将数据包 " << packet->GetUid() << " 从WiFi网络转发到水声网络");
            }
            else
            {
                NS_LOG_WARN("中继: 转发数据包 " << packet->GetUid() << " 从WiFi网络到水声网络失败 (MAC层忙碌或冲突)");
                // 注意：ALOHA协议在高负载下经常失败是正常现象
            }
        }
        else
        {
            NS_LOG_ERROR("中继: UAN设备或MAC层未正确配置");
        }
    }
    else
    {
        NS_LOG_ERROR("中继: UAN设备不存在");
    }
}

// 主仿真函数
int main(int argc, char* argv[])
{
    // 仿真参数
    double simulationTime = 200.0; // 秒 - 延长到200秒
    uint32_t numUanNodes = 2;      // UAN节点数量
    uint32_t numShipNodes = 1;     // 船舶节点数量
    uint32_t numUavNodes = 1;      // 无人机节点数量
    uint32_t numRelayNodes = 1;    // 中继节点数量

    // 命令行参数
    CommandLine cmd(__FILE__);
    cmd.AddValue("simulationTime", "仿真时间（秒）", simulationTime);
    cmd.AddValue("numUanNodes", "UAN节点数量", numUanNodes);
    cmd.AddValue("numShipNodes", "船舶节点数量", numShipNodes);
    cmd.AddValue("numUavNodes", "无人机节点数量", numUavNodes);
    cmd.Parse(argc, argv);

    // 启用日志记录
    LogComponentEnable("UnderwaterRelaySimulation", LOG_LEVEL_INFO);

    NS_LOG_INFO("开始水下-水面-空中通信仿真");
    NS_LOG_INFO("仿真时间: " << simulationTime << " 秒");
    NS_LOG_INFO("水声节点: " << numUanNodes << "个, 船舶节点: " << numShipNodes << "个, 无人机节点: " << numUavNodes << "个");

    // 创建节点
    NodeContainer uanNodes;
    NodeContainer relayNodes;
    NodeContainer shipNodes;
    NodeContainer uavNodes;

    uanNodes.Create(numUanNodes);
    relayNodes.Create(numRelayNodes);
    shipNodes.Create(numShipNodes);
    uavNodes.Create(numUavNodes);

    NS_LOG_INFO("已创建 " << (numUanNodes + numRelayNodes + numShipNodes + numUavNodes) << " 个节点");

    // 设置移动模型和位置
    MobilityHelper mobility;

    // UAN节点 - 水下位置（负Z坐标）
    Ptr<ListPositionAllocator> uanPositionAlloc = CreateObject<ListPositionAllocator>();
    for (uint32_t i = 0; i < numUanNodes; ++i)
    {
        // 将UAN节点放置在不同深度的水下
        double x = 50.0 + i * 100.0;  // 沿X轴分布
        double y = 50.0;               // 固定Y位置
        double z = -20.0 - i * 10.0;   // 水下（负Z坐标）
        uanPositionAlloc->Add(Vector(x, y, z));
        NS_LOG_INFO("水声节点 " << i << " 位置: (" << x << ", " << y << ", " << z << ")");
    }
    mobility.SetPositionAllocator(uanPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(uanNodes);

    // 中继节点 - 水面位置（Z = 0）
    Ptr<ListPositionAllocator> relayPositionAlloc = CreateObject<ListPositionAllocator>();
    for (uint32_t i = 0; i < numRelayNodes; ++i)
    {
        double x = 100.0 + i * 50.0;   // 位于UAN和水面节点之间
        double y = 50.0;
        double z = 0.0;                // 在水面
        relayPositionAlloc->Add(Vector(x, y, z));
        NS_LOG_INFO("中继节点 " << i << " 位置: (" << x << ", " << y << ", " << z << ")");
    }
    mobility.SetPositionAllocator(relayPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(relayNodes);

    // Ship nodes - surface positions (Z = 1-2m above water)
    Ptr<ListPositionAllocator> shipPositionAlloc = CreateObject<ListPositionAllocator>();
    for (uint32_t i = 0; i < numShipNodes; ++i)
    {
        double x = 150.0 + i * 80.0;
        double y = 50.0;
        double z = 2.0;                // Above water surface
        shipPositionAlloc->Add(Vector(x, y, z));
        NS_LOG_INFO("船舶节点 " << i << " 位置: (" << x << ", " << y << ", " << z << ")");
    }
    mobility.SetPositionAllocator(shipPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(shipNodes);

    // UAV nodes - aerial positions (Z = 50-100m above water)
    Ptr<ListPositionAllocator> uavPositionAlloc = CreateObject<ListPositionAllocator>();
    for (uint32_t i = 0; i < numUavNodes; ++i)
    {
        double x = 200.0 + i * 100.0;
        double y = 50.0;
        double z = 50.0 + i * 25.0;    // High above water
        uavPositionAlloc->Add(Vector(x, y, z));
        NS_LOG_INFO("无人机节点 " << i << " 位置: (" << x << ", " << y << ", " << z << ")");
    }
    mobility.SetPositionAllocator(uavPositionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(uavNodes);

    // 配置水下通信的UAN网络
    NS_LOG_INFO("正在配置UAN网络...");

    // 创建具有更现实传播模型的UAN信道
    Ptr<UanChannel> uanChannel = CreateObject<UanChannel>();
    Ptr<UanPropModelThorp> uanProp = CreateObject<UanPropModelThorp>();
    uanChannel->SetPropagationModel(uanProp);

    // 添加噪声模型以提高现实性
    Ptr<UanNoiseModelDefault> noise = CreateObject<UanNoiseModelDefault>();
    uanChannel->SetNoiseModel(noise);

    // 使用简化设置配置UAN助手
    UanHelper uan;

    // 使用默认UAN模式
    UanModesList defaultModes = UanPhyGen::GetDefaultModes();

    // 使用默认设置配置UAN物理层
    uan.SetPhy("ns3::UanPhyGen",
               "SupportedModes", UanModesListValue(defaultModes));

    // 使用CW-MAC协议配置UAN MAC层（比ALOHA更好的冲突避免）
    uan.SetMac("ns3::UanMacCw",
               "CW", UintegerValue(10),        // 竞争窗口大小
               "SlotTime", TimeValue(MilliSeconds(20)));  // 时隙时间

    // 在水下节点和中继节点上安装UAN设备
    NetDeviceContainer uanDevices;

    // 在UAN节点上安装
    NetDeviceContainer uanNodeDevices = uan.Install(uanNodes, uanChannel);
    uanDevices.Add(uanNodeDevices);

    // 在中继节点上安装（它们需要UAN接口进行水下通信）
    NetDeviceContainer relayUanDevices = uan.Install(relayNodes, uanChannel);
    uanDevices.Add(relayUanDevices);

    NS_LOG_INFO("UAN网络配置完成，共 " << uanDevices.GetN() << " 个设备");

    // 调试：检查UAN设备配置
    for (uint32_t i = 0; i < uanDevices.GetN(); ++i)
    {
        Ptr<UanNetDevice> uanDev = DynamicCast<UanNetDevice>(uanDevices.Get(i));
        if (uanDev)
        {
            NS_LOG_INFO("UAN设备 " << i << ": 地址=" << uanDev->GetAddress()
                       << ", MTU=" << uanDev->GetMtu()
                       << ", 信道=" << (uanDev->GetChannel() ? "正常" : "空"));
        }
    }

    // 配置水面和空中通信的WiFi网络
    NS_LOG_INFO("正在配置WiFi网络...");

    // 创建WiFi信道
    YansWifiChannelHelper wifiChannel = YansWifiChannelHelper::Default();
    YansWifiPhyHelper wifiPhy;
    wifiPhy.SetChannel(wifiChannel.Create());

    // 配置WiFi标准和数据速率
    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211n);
    wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                 "DataMode", StringValue("HtMcs7"),
                                 "ControlMode", StringValue("HtMcs0"));

    // 配置WiFi MAC为自组织模式
    WifiMacHelper wifiMac;
    wifiMac.SetType("ns3::AdhocWifiMac");

    // 安装WiFi设备
    NetDeviceContainer wifiDevices;

    // 在中继节点上安装（它们需要WiFi接口进行水面通信）
    NetDeviceContainer relayWifiDevices = wifi.Install(wifiPhy, wifiMac, relayNodes);
    wifiDevices.Add(relayWifiDevices);

    // 在船舶节点上安装
    NetDeviceContainer shipWifiDevices = wifi.Install(wifiPhy, wifiMac, shipNodes);
    wifiDevices.Add(shipWifiDevices);

    // 在无人机节点上安装
    NetDeviceContainer uavWifiDevices = wifi.Install(wifiPhy, wifiMac, uavNodes);
    wifiDevices.Add(uavWifiDevices);

    NS_LOG_INFO("WiFi网络配置完成，共 " << wifiDevices.GetN() << " 个设备");

    // 在中继节点上安装中继应用程序
    NS_LOG_INFO("正在安装中继应用程序...");

    for (uint32_t i = 0; i < numRelayNodes; ++i)
    {
        Ptr<RelayApplication> relayApp = CreateObject<RelayApplication>();

        // 获取此中继节点的UAN和WiFi设备
        Ptr<NetDevice> uanDev = relayUanDevices.Get(i);
        Ptr<NetDevice> wifiDev = relayWifiDevices.Get(i);

        // 将两种设备绑定到中继应用程序
        relayApp->SetUanDevice(uanDev);
        relayApp->SetWifiDevice(wifiDev);

        // 将中继应用程序添加到节点
        relayNodes.Get(i)->AddApplication(relayApp);
        relayApp->SetStartTime(Seconds(1.0));
        relayApp->SetStopTime(Seconds(simulationTime - 1.0));

        NS_LOG_INFO("中继应用程序已安装在中继节点 " << i << " 上");
    }

    // Install Internet stack on all nodes
    NS_LOG_INFO("Installing Internet stack...");
    InternetStackHelper internet;
    internet.Install(uanNodes);
    internet.Install(relayNodes);
    internet.Install(shipNodes);
    internet.Install(uavNodes);

    // Assign IP addresses
    NS_LOG_INFO("Assigning IP addresses...");
    Ipv4AddressHelper ipv4;

    // UAN network subnet (********/24)
    ipv4.SetBase("********", "*************");
    Ipv4InterfaceContainer uanInterfaces = ipv4.Assign(uanNodeDevices);
    Ipv4InterfaceContainer relayUanInterfaces = ipv4.Assign(relayUanDevices);

    // WiFi network subnet (********/24)
    ipv4.SetBase("********", "*************");
    Ipv4InterfaceContainer relayWifiInterfaces = ipv4.Assign(relayWifiDevices);
    Ipv4InterfaceContainer shipWifiInterfaces = ipv4.Assign(shipWifiDevices);
    Ipv4InterfaceContainer uavWifiInterfaces = ipv4.Assign(uavWifiDevices);

    // Print IP address assignments
    NS_LOG_INFO("IP Address Assignments:");
    for (uint32_t i = 0; i < numUanNodes; ++i)
    {
        NS_LOG_INFO("UAN Node " << i << ": " << uanInterfaces.GetAddress(i));
    }
    for (uint32_t i = 0; i < numRelayNodes; ++i)
    {
        NS_LOG_INFO("Relay Node " << i << " UAN interface: " << relayUanInterfaces.GetAddress(i));
        NS_LOG_INFO("Relay Node " << i << " WiFi interface: " << relayWifiInterfaces.GetAddress(i));
    }
    for (uint32_t i = 0; i < numShipNodes; ++i)
    {
        NS_LOG_INFO("Ship Node " << i << ": " << shipWifiInterfaces.GetAddress(i));
    }
    for (uint32_t i = 0; i < numUavNodes; ++i)
    {
        NS_LOG_INFO("UAV Node " << i << ": " << uavWifiInterfaces.GetAddress(i));
    }

    // Configure ARP cache and add static ARP entries for UAN network
    // This is important for underwater networks where ARP may not work reliably

    // Configure ARP cache timeout for UAN nodes
    NodeContainer::Iterator node = uanNodes.Begin();
    while (node != uanNodes.End())
    {
        Ptr<ArpCache> arpCache = (*node)->GetObject<Ipv4L3Protocol>()->GetInterface(1)->GetArpCache();
        arpCache->SetWaitReplyTimeout(Seconds(10));
        arpCache->SetDeadTimeout(Seconds(60));
        arpCache->SetAliveTimeout(Seconds(120));
        node++;
    }

    // Configure ARP cache timeout for relay nodes
    node = relayNodes.Begin();
    while (node != relayNodes.End())
    {
        // UAN interface (interface 1)
        Ptr<ArpCache> arpCache = (*node)->GetObject<Ipv4L3Protocol>()->GetInterface(1)->GetArpCache();
        arpCache->SetWaitReplyTimeout(Seconds(10));
        arpCache->SetDeadTimeout(Seconds(60));
        arpCache->SetAliveTimeout(Seconds(120));
        node++;
    }

    // Add static ARP entries for UAN network to avoid ARP resolution issues
    // UAN Node 0 -> UAN Node 1
    Ptr<ArpCache> arpCache0 = uanNodes.Get(0)->GetObject<Ipv4L3Protocol>()->GetInterface(1)->GetArpCache();
    ArpCache::Entry* entry = arpCache0->Add(uanInterfaces.GetAddress(1));
    entry->SetMacAddress(uanDevices.Get(1)->GetAddress());
    entry->MarkPermanent();

    // UAN Node 1 -> UAN Node 0
    Ptr<ArpCache> arpCache1 = uanNodes.Get(1)->GetObject<Ipv4L3Protocol>()->GetInterface(1)->GetArpCache();
    entry = arpCache1->Add(uanInterfaces.GetAddress(0));
    entry->SetMacAddress(uanDevices.Get(0)->GetAddress());
    entry->MarkPermanent();

    // UAN Node 0 -> Relay
    entry = arpCache0->Add(relayUanInterfaces.GetAddress(0));
    entry->SetMacAddress(uanDevices.Get(2)->GetAddress());
    entry->MarkPermanent();

    // UAN Node 1 -> Relay
    entry = arpCache1->Add(relayUanInterfaces.GetAddress(0));
    entry->SetMacAddress(uanDevices.Get(2)->GetAddress());
    entry->MarkPermanent();

    // Relay -> UAN Nodes
    Ptr<ArpCache> arpCacheRelay = relayNodes.Get(0)->GetObject<Ipv4L3Protocol>()->GetInterface(1)->GetArpCache();
    entry = arpCacheRelay->Add(uanInterfaces.GetAddress(0));
    entry->SetMacAddress(uanDevices.Get(0)->GetAddress());
    entry->MarkPermanent();
    entry = arpCacheRelay->Add(uanInterfaces.GetAddress(1));
    entry->SetMacAddress(uanDevices.Get(1)->GetAddress());
    entry->MarkPermanent();

    // Enable global routing
    Ipv4GlobalRoutingHelper::PopulateRoutingTables();

    // Create applications for communication testing
    NS_LOG_INFO("Creating applications...");

    uint16_t port = 9;

    // Install UDP servers on all nodes to receive packets
    UdpServerHelper server(port);
    ApplicationContainer serverApps;

    // Servers on UAN nodes
    ApplicationContainer uanServers = server.Install(uanNodes);
    serverApps.Add(uanServers);
    NS_LOG_INFO("Installed " << uanServers.GetN() << " UDP servers on UAN nodes");

    // Servers on ship nodes
    ApplicationContainer shipServers = server.Install(shipNodes);
    serverApps.Add(shipServers);
    NS_LOG_INFO("Installed " << shipServers.GetN() << " UDP servers on ship nodes");

    // Servers on UAV nodes
    ApplicationContainer uavServers = server.Install(uavNodes);
    serverApps.Add(uavServers);
    NS_LOG_INFO("Installed " << uavServers.GetN() << " UDP servers on UAV nodes");

    // Also install server on relay nodes
    ApplicationContainer relayServers = server.Install(relayNodes);
    serverApps.Add(relayServers);
    NS_LOG_INFO("Installed " << relayServers.GetN() << " UDP servers on relay nodes");

    serverApps.Start(Seconds(1.0));
    serverApps.Stop(Seconds(simulationTime));

    // Create UDP echo clients for sending packets within same network first
    ApplicationContainer clientApps;

    // 为UAN节点创建持续的UDP通信应用 - 使用独立端口避免冲突
    // 首先在目标节点上安装UDP服务器
    UdpServerHelper uanServer1(9080);  // 使用不同的端口
    ApplicationContainer uanServerApps1 = uanServer1.Install(uanNodes.Get(1));
    uanServerApps1.Start(Seconds(1.0));
    uanServerApps1.Stop(Seconds(simulationTime));

    UdpServerHelper uanServer2(9081);  // 使用不同的端口
    ApplicationContainer uanServerApps2 = uanServer2.Install(uanNodes.Get(0));
    uanServerApps2.Start(Seconds(1.0));
    uanServerApps2.Stop(Seconds(simulationTime));

    // UAN节点0向节点1发送数据包 - 降低频率以减少冲突
    UdpClientHelper uanClient1(uanInterfaces.GetAddress(1), 9080);
    uanClient1.SetAttribute("MaxPackets", UintegerValue(100));  // 减少数据包数量
    uanClient1.SetAttribute("Interval", TimeValue(Seconds(2.0)));  // 降低到每2秒1个包
    uanClient1.SetAttribute("PacketSize", UintegerValue(32));  // 减小数据包大小
    ApplicationContainer uanClientApps1 = uanClient1.Install(uanNodes.Get(0));
    uanClientApps1.Start(Seconds(5.0));
    uanClientApps1.Stop(Seconds(simulationTime - 1.0));  // 运行到199秒
    NS_LOG_INFO("水声通信配置: 节点0 将向节点1发送数据包，目标地址 " << uanInterfaces.GetAddress(1) << " 端口9080");

    // UAN节点1向节点0发送数据包 - 降低频率以减少冲突
    UdpClientHelper uanClient2(uanInterfaces.GetAddress(0), 9081);
    uanClient2.SetAttribute("MaxPackets", UintegerValue(80));  // 减少数据包数量
    uanClient2.SetAttribute("Interval", TimeValue(Seconds(2.5)));  // 降低到每2.5秒1个包
    uanClient2.SetAttribute("PacketSize", UintegerValue(32));  // 减小数据包大小
    ApplicationContainer uanClientApps2 = uanClient2.Install(uanNodes.Get(1));
    uanClientApps2.Start(Seconds(7.5));  // 更大的错开时间
    uanClientApps2.Stop(Seconds(simulationTime - 1.0));  // 运行到199秒
    NS_LOG_INFO("水声通信配置: 节点1 将向节点0发送数据包，目标地址 " << uanInterfaces.GetAddress(0) << " 端口9081");

    // Ship and UAV nodes communicate with each other - 适度的数据包频率
    for (uint32_t i = 0; i < numShipNodes; ++i)
    {
        for (uint32_t j = 0; j < numUavNodes; ++j)
        {
            UdpClientHelper client(uavWifiInterfaces.GetAddress(j), port);
            client.SetAttribute("MaxPackets", UintegerValue(100));  // 适度数量
            client.SetAttribute("Interval", TimeValue(Seconds(1.5)));  // 降低频率
            client.SetAttribute("PacketSize", UintegerValue(128));
            clientApps.Add(client.Install(shipNodes.Get(i)));
        }
    }

    // UAV nodes communicate with ship nodes - 适度的数据包频率
    for (uint32_t i = 0; i < numUavNodes; ++i)
    {
        for (uint32_t j = 0; j < numShipNodes; ++j)
        {
            UdpClientHelper client(shipWifiInterfaces.GetAddress(j), port);
            client.SetAttribute("MaxPackets", UintegerValue(80));  // 适度数量
            client.SetAttribute("Interval", TimeValue(Seconds(2.0)));  // 降低频率
            client.SetAttribute("PacketSize", UintegerValue(64));
            clientApps.Add(client.Install(uavNodes.Get(i)));
        }
    }

    // Add ship and UAV to relay communication - 大幅降低频率以适应UAN网络
    for (uint32_t i = 0; i < numShipNodes; ++i)
    {
        UdpClientHelper shipToRelayClient(relayWifiInterfaces.GetAddress(0), 9);
        shipToRelayClient.SetAttribute("MaxPackets", UintegerValue(40));  // 进一步减少
        shipToRelayClient.SetAttribute("Interval", TimeValue(Seconds(5.0)));  // 降低到每5秒一个包
        shipToRelayClient.SetAttribute("PacketSize", UintegerValue(32));  // 减小数据包大小
        clientApps.Add(shipToRelayClient.Install(shipNodes.Get(i)));
        NS_LOG_INFO("船舶到中继通信: 每5秒发送一个数据包");
    }

    for (uint32_t i = 0; i < numUavNodes; ++i)
    {
        UdpClientHelper uavToRelayClient(relayWifiInterfaces.GetAddress(0), 9);
        uavToRelayClient.SetAttribute("MaxPackets", UintegerValue(30));  // 进一步减少
        uavToRelayClient.SetAttribute("Interval", TimeValue(Seconds(6.0)));  // 降低到每6秒一个包
        uavToRelayClient.SetAttribute("PacketSize", UintegerValue(32));  // 减小数据包大小
        clientApps.Add(uavToRelayClient.Install(uavNodes.Get(i)));
        NS_LOG_INFO("无人机到中继通信: 每6秒发送一个数据包");
    }

    clientApps.Start(Seconds(5.0));
    clientApps.Stop(Seconds(simulationTime - 5.0));

    // Enable packet tracing
    NS_LOG_INFO("Enabling packet tracing...");

    // Connect UAN packet traces
    for (uint32_t i = 0; i < uanDevices.GetN(); ++i)
    {
        Ptr<UanNetDevice> uanDev = DynamicCast<UanNetDevice>(uanDevices.Get(i));
        if (uanDev && uanDev->GetPhy())
        {
            uanDev->GetPhy()->TraceConnectWithoutContext("PhyTxBegin", MakeCallback(&UanTxTrace));
            uanDev->GetPhy()->TraceConnectWithoutContext("PhyRxEnd", MakeCallback(&UanRxTrace));
            NS_LOG_INFO("Connected UAN traces for device " << i);
        }
        else
        {
            NS_LOG_WARN("Failed to connect UAN traces for device " << i);
        }
    }

    // Connect WiFi packet traces
    for (uint32_t i = 0; i < wifiDevices.GetN(); ++i)
    {
        Ptr<WifiNetDevice> wifiDev = DynamicCast<WifiNetDevice>(wifiDevices.Get(i));
        if (wifiDev)
        {
            wifiDev->GetMac()->TraceConnectWithoutContext("MacTx", MakeCallback(&WifiTxTrace));
            wifiDev->GetMac()->TraceConnectWithoutContext("MacRx", MakeCallback(&WifiRxTrace));
        }
    }

    // Enable ASCII tracing for detailed packet analysis
    AsciiTraceHelper ascii;
    Ptr<OutputStreamWrapper> stream = ascii.CreateFileStream("underwater-relay-trace.tr");

    // Enable UAN ASCII tracing
    UanHelper::EnableAscii(*stream->GetStream(), uanDevices);

    // Enable WiFi ASCII tracing
    wifiPhy.EnableAsciiAll(stream);

    // Run simulation
    NS_LOG_INFO("Starting simulation for " << simulationTime << " seconds...");
    Simulator::Stop(Seconds(simulationTime));
    Simulator::Run();

    // 打印详细的最终统计信息
    NS_LOG_INFO("=== 详细仿真结果 ===");
    NS_LOG_INFO("=== 整体网络统计 ===");
    NS_LOG_INFO("水声网络发送数据包总数: " << g_uanTxPackets);
    NS_LOG_INFO("水声网络接收数据包总数: " << g_uanRxPackets);
    NS_LOG_INFO("WiFi网络发送数据包总数: " << g_wifiTxPackets);
    NS_LOG_INFO("WiFi网络接收数据包总数: " << g_wifiRxPackets);
    NS_LOG_INFO("中继转发数据包总数: " << g_relayForwardedPackets);

    NS_LOG_INFO("=== 各节点类型统计 ===");
    NS_LOG_INFO("水声节点(水下): 发送=" << g_uanTxPackets << ", 接收=" << g_uanRxPackets);
    NS_LOG_INFO("中继节点(水面): 转发=" << g_relayForwardedPackets);
    NS_LOG_INFO("船舶节点(水面): 参与WiFi通信");
    NS_LOG_INFO("无人机节点(空中): 参与WiFi通信");

    NS_LOG_INFO("=== 网络性能指标 ===");
    if (g_uanTxPackets > 0) {
        double uanDeliveryRatio = (double)g_uanRxPackets / g_uanTxPackets * 100.0;
        NS_LOG_INFO("水声网络数据包投递率: " << std::fixed << std::setprecision(2) << uanDeliveryRatio << "%");
    }
    if (g_wifiTxPackets > 0) {
        double wifiDeliveryRatio = (double)g_wifiRxPackets / g_wifiTxPackets * 100.0;
        NS_LOG_INFO("WiFi网络数据包投递率: " << std::fixed << std::setprecision(2) << wifiDeliveryRatio << "%");
    }
    if (g_relayForwardedPackets > 0) {
        NS_LOG_INFO("中继转发效率: " << g_relayForwardedPackets << " 个数据包成功转发");
    }

    // 验证所有节点类型是否都有活动
    bool allNodesActive = (g_uanTxPackets > 0) && (g_uanRxPackets > 0) &&
                         (g_wifiTxPackets > 0) && (g_wifiRxPackets > 0) &&
                         (g_relayForwardedPackets > 0);

    if (allNodesActive)
    {
        NS_LOG_INFO("成功: 所有节点类型(水声、中继、船舶、无人机)都已发送和接收数据包!");
    }
    else
    {
        NS_LOG_WARN("警告: 某些节点类型可能没有足够的数据包活动。");
    }

    Simulator::Destroy();
    return 0;
}
