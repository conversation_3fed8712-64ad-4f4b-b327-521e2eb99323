#include "ns3/core-module.h"
#include "ns3/mobility-module.h"
#include "ns3/wifi-module.h"
#include "ns3/internet-module.h"
#include "ns3/applications-module.h"
#include "ns3/propagation-loss-model.h"
#include "ns3/gnuplot.h"
#include "ns3/vector.h"
#include <fstream>
#include <map>
#include <vector>
#include <string>

using namespace ns3;

NS_LOG_COMPONENT_DEFINE("DroneCommunicationSimulation");

// 定义天气条件和轨迹模式枚举
enum WeatherCondition { SUNNY, RAINY, FOGGY };
enum TrajectoryMode { RANDOM_WALK, LINEAR_PATROL, CIRCULAR_PATROL, WAYPOINT };

// 转换函数
WeatherCondition IntToWeather(int value) {
    switch (value) {
        case 0: return SUNNY;
        case 1: return RAINY;
        case 2: return FOGGY;
        default: return SUNNY;
    }
}
TrajectoryMode IntToTrajectory(int value) {
    switch (value) {
        case 0: return RANDOM_WALK;
        case 1: return LINEAR_PATROL;
        case 2: return CIRCULAR_PATROL;
        case 3: return WAYPOINT;
        default: return RANDOM_WALK;
    }
}

// 仿真参数定义
const double SIMULATION_TIME = 300.0;    // 仿真时间（秒）
const uint32_t PACKET_SIZE = 1046;       // 数据包大小（字节）
const double DATA_RATE = 54.0;            // 数据速率（Mbps）
const double SCENE_WIDTH = 200.0;        // 场景宽度（米）
const double SCENE_HEIGHT = 200.0;       // 场景高度（米）
const double DRONE_MIN_HEIGHT = 10.0;    // 无人机最低高度（米）
const double DRONE_MAX_HEIGHT = 100.0;   // 无人机最高高度（米）

// 障碍物模型（树木和建筑物）
struct Tree {
    Vector position;
    double radius;
};

struct Building {
    Vector position;
    double width;
    double depth;
    double height;
};

// 自定义向量点积计算函数
double DotProduct(const Vector& a, const Vector& b) {
    return a.x * b.x + a.y * b.y + a.z * b.z;
}

std::vector<Tree> trees;
std::vector<Building> buildings;

double PointToLineDistance(const Vector& point, const Vector& lineStart, const Vector& lineEnd);

// 障碍物阴影损耗模型
class ObstacleShadowingLossModel : public PropagationLossModel {
public:
    static TypeId GetTypeId(void);
    ObstacleShadowingLossModel();
    
    // 设置损耗参数（不同障碍物类型的损耗值）
    void SetLossParameters(double smallTree, double largeTree, double smallBuilding, double largeBuilding) {
        m_smallTreeLoss = smallTree;
        m_largeTreeLoss = largeTree;
        m_smallBuildingLoss = smallBuilding;
        m_largeBuildingLoss = largeBuilding;
    }
    
    // 分配随机流
    int64_t DoAssignStreams(int64_t stream) override {
        m_rand->SetStream(stream);
        return 1;
    }

private:
    double DoCalcRxPower(double txPowerDbm,
                         Ptr<MobilityModel> a,
                         Ptr<MobilityModel> b) const override;
    
    double m_smallTreeLoss;    // 小型树木损耗（dB）
    double m_largeTreeLoss;    // 大型树木损耗（dB）
    double m_smallBuildingLoss;// 小型建筑物损耗（dB）
    double m_largeBuildingLoss;// 大型建筑物损耗（dB）
    Ptr<UniformRandomVariable> m_rand; // 随机变量生成器
};


TypeId ObstacleShadowingLossModel::GetTypeId(void) {
    static TypeId tid = TypeId("ns3::ObstacleShadowingLossModel")
        .SetParent<PropagationLossModel>()
        .SetGroupName("Propagation")
        .AddConstructor<ObstacleShadowingLossModel>()
        .AddAttribute("SmallTreeLoss", "Loss for small trees (dB)",
                      DoubleValue(5.0),
                      MakeDoubleAccessor(&ObstacleShadowingLossModel::m_smallTreeLoss),
                      MakeDoubleChecker<double>())
        .AddAttribute("LargeTreeLoss", "Loss for large trees (dB)",
                      DoubleValue(15.0),
                      MakeDoubleAccessor(&ObstacleShadowingLossModel::m_largeTreeLoss),
                      MakeDoubleChecker<double>())
        .AddAttribute("SmallBuildingLoss", "Loss for small buildings (dB)",
                      DoubleValue(20.0),
                      MakeDoubleAccessor(&ObstacleShadowingLossModel::m_smallBuildingLoss),
                      MakeDoubleChecker<double>())
        .AddAttribute("LargeBuildingLoss", "Loss for large buildings (dB)",
                      DoubleValue(40.0),
                      MakeDoubleAccessor(&ObstacleShadowingLossModel::m_largeBuildingLoss),
                      MakeDoubleChecker<double>());
    return tid;
}

// 添加这行代码以注册该类
NS_OBJECT_ENSURE_REGISTERED(ObstacleShadowingLossModel);

ObstacleShadowingLossModel::ObstacleShadowingLossModel() 
    : m_smallTreeLoss(5.0), m_largeTreeLoss(15.0),
      m_smallBuildingLoss(20.0), m_largeBuildingLoss(40.0) {
    m_rand = CreateObject<UniformRandomVariable>();
}

double ObstacleShadowingLossModel::DoCalcRxPower(double txPowerDbm,
                                                Ptr<MobilityModel> a,
                                                Ptr<MobilityModel> b) const {
    double totalLoss = 0.0;
    Vector posA = a->GetPosition();
    Vector posB = b->GetPosition();
    
    // 检查树木障碍物
    for (const auto& tree : trees) { 
        double distance = PointToLineDistance(tree.position, posA, posB); 
        if (distance < tree.radius) {
            totalLoss += (tree.radius < 5.0) ? m_smallTreeLoss : m_largeTreeLoss;
        }
    }
    
    // 检查建筑物障碍物
    for (const auto& building : buildings) {
        double distance = PointToLineDistance(building.position, posA, posB);
        if (distance < (building.width/2)) {
            double size = building.width * building.depth;
            totalLoss += (size < 100.0) ? m_smallBuildingLoss : m_largeBuildingLoss;
        }
    }
    
    totalLoss += m_rand->GetValue(-2.0, 2.0); // 随机阴影衰落
    return txPowerDbm - totalLoss;
}

// 辅助函数：计算点到线段的距离
double PointToLineDistance(const Vector& point, const Vector& lineStart, const Vector& lineEnd) {
    Vector lineVec = lineEnd - lineStart;
    Vector pointVec = point - lineStart;
    // 调用自定义的 DotProduct 函数（无需加 ns3:: 前缀）
    double t = std::max(0.0, std::min(1.0, DotProduct(pointVec, lineVec) / DotProduct(lineVec, lineVec)));
    // 修复Vector乘法运算：手动计算标量乘法
    Vector projection = Vector(lineStart.x + t * lineVec.x,
                              lineStart.y + t * lineVec.y,
                              lineStart.z + t * lineVec.z);
    return CalculateDistance(point, projection);
}

// 辅助函数：检查点是否在建筑物内
bool IsPointInBuilding(const Building& b, const Vector& start, const Vector& end) {
    Vector center = b.position;
    double halfWidth = b.width / 2.0;
    double halfDepth = b.depth / 2.0;
    
    // 检查起点是否在建筑物内
    bool startIn = (start.x >= center.x - halfWidth && start.x <= center.x + halfWidth &&
                   start.y >= center.y - halfDepth && start.y <= center.y + halfDepth);
    
    // 检查终点是否在建筑物内
    bool endIn = (end.x >= center.x - halfWidth && end.x <= center.x + halfWidth &&
                 end.y >= center.y - halfDepth && end.y <= center.y + halfDepth);
                 
    return startIn || endIn;
}

// 生成障碍物
void GenerateObstacles() {
    // 创建局部随机变量
    Ptr<UniformRandomVariable> rand = CreateObject<UniformRandomVariable>();
    
    // 生成随机树木
    for (int i = 0; i < 20; ++i) {
        Tree tree;
        tree.position = Vector(
            rand->GetValue(0, SCENE_WIDTH),
            rand->GetValue(0, SCENE_HEIGHT),
            0.0
        );
        tree.radius = rand->GetValue(1.0, 8.0);
        trees.push_back(tree);
    }
    
    // 生成随机建筑物
    for (int i = 0; i < 5; ++i) {
        Building building;
        building.position = Vector(
            rand->GetValue(0, SCENE_WIDTH),
            rand->GetValue(0, SCENE_HEIGHT),
            0.0
        );
        building.width = rand->GetValue(5.0, 30.0);
        building.depth = rand->GetValue(5.0, 30.0);
        building.height = rand->GetValue(5.0, 20.0);
        buildings.push_back(building);
    }
}

// 性能指标跟踪类
class PerformanceMonitor : public Object {
public:
    static TypeId GetTypeId(void) {
        static TypeId tid = TypeId("ns3::PerformanceMonitor")
            .SetParent<Object>()
            .SetGroupName("Stats")
            .AddConstructor<PerformanceMonitor>();
        return tid;
    }
    PerformanceMonitor(std::string outputDir = ".") : 
        m_totalPacketsSent(0), m_totalPacketsReceived(0),
        m_totalBytesReceived(0), m_firstPacketTime(Seconds(0)),
        m_outputDir(outputDir) {
        // 创建输出目录
        std::string cmd = "mkdir -p " + m_outputDir;
        int ret = system(cmd.c_str());
	(void)ret; // 显式忽略返回值，同时避免编译器警告
    }
    
    // 记录发送的数据包
void PacketSent(Ptr<const Packet> packet) {  // 移除const Address& address参数
    m_totalPacketsSent++;
    m_sendTimes[packet->GetUid()] = Simulator::Now();
    
    // 直接使用0号节点作为发送方（无需从address获取）
    Ptr<Node> sender = NodeList::GetNode(0); 
    if (sender) {
        Ptr<MobilityModel> senderMob = sender->GetObject<MobilityModel>();
        if (senderMob) {
            m_senderPositions[packet->GetUid()] = senderMob->GetPosition();
        }
    }
}

    // 记录接收的数据包
    void PacketReceived(Ptr<const Packet> packet, const Address& address) {
        uint32_t uid = packet->GetUid();
        if (m_sendTimes.find(uid) != m_sendTimes.end()) {
            m_totalPacketsReceived++;
            m_totalBytesReceived += packet->GetSize();
            
            Time delay = Simulator::Now() - m_sendTimes[uid];
            m_delayValues.push_back(delay.GetSeconds());
            
            if (m_firstPacketTime.GetSeconds() == 0) {
                m_firstPacketTime = Simulator::Now();
            }
            
            // 直接获取接收节点（1号节点为接收方）
            Ptr<Node> receiver = NodeList::GetNode(1); 
            if (receiver) {
                Ptr<MobilityModel> receiverMob = receiver->GetObject<MobilityModel>();
                if (receiverMob) {
                    Vector senderPos = m_senderPositions[uid];
                    Vector receiverPos = receiverMob->GetPosition();
                    double distance = CalculateDistance(senderPos, receiverPos);
                    m_distanceVsDelay[distance] = delay.GetSeconds();
                    m_distanceVsLoss[distance]++;
                }
            }
            
            m_sendTimes.erase(uid);
            m_senderPositions.erase(uid);
        }
    }
    
    // 记录无人机位置（用于轨迹可视化）
    void LogDronePositions(Ptr<Node> drone, int id) {
        if (!drone) return;
        
        Ptr<MobilityModel> mob = drone->GetObject<MobilityModel>();
        if (mob) {
            Vector pos = mob->GetPosition();
            std::ofstream outFile(m_outputDir + "/drone_" + std::to_string(id) + "_trajectory.txt", std::ios::app);
            if (outFile.is_open()) {
                outFile << Simulator::Now().GetSeconds() << " " 
                        << pos.x << " " << pos.y << " " << pos.z << std::endl;
                outFile.close();
            }
        }
        
        // 每0.5秒记录一次位置
        Simulator::Schedule(Seconds(0.5), &PerformanceMonitor::LogDronePositions, this, drone, id);
    }
    
    // 生成可视化图表
    void GeneratePlots(WeatherCondition weather, TrajectoryMode trajectory) {
        std::string weatherStr[] = {"sunny", "rainy", "foggy"};
        std::string trajectoryStr[] = {"random", "linear", "circular", "waypoint"};
        
        std::string weatherName = weatherStr[weather];
        std::string trajectoryName = trajectoryStr[trajectory];
        
        // 1. 时延 vs 距离 图表
        {
            Gnuplot plot(m_outputDir + "/delay_vs_distance_" + weatherName + "_" + trajectoryName + ".png");
            plot.SetTitle("Packet Delay vs Distance (" + weatherName + ", " + trajectoryName + ")");
            plot.SetTerminal("pngcairo enhanced font 'Arial,10'");
            plot.SetLegend("Distance (m)", "Delay (s)");
            
            Gnuplot2dDataset dataset;
            dataset.SetTitle("Delay");
            dataset.SetStyle(Gnuplot2dDataset::POINTS);
            
            for (const auto& entry : m_distanceVsDelay) {
                dataset.Add(entry.first, entry.second);
            }
            
            plot.AddDataset(dataset);
            std::ofstream delayFile(m_outputDir + "/delay_vs_distance_" + weatherName + "_" + trajectoryName + ".plt");
            plot.GenerateOutput(delayFile);
        }
        
        // 2. 丢包率 vs 距离 图表
        {
            Gnuplot plot(m_outputDir + "/loss_vs_distance_" + weatherName + "_" + trajectoryName + ".png");
            plot.SetTitle("Packet Loss Rate vs Distance (" + weatherName + ", " + trajectoryName + ")");
            plot.SetTerminal("pngcairo enhanced font 'Arial,10'");
            plot.SetLegend("Distance (m)", "Loss Rate");
            
            Gnuplot2dDataset dataset;
            dataset.SetTitle("Loss Rate");
            dataset.SetStyle(Gnuplot2dDataset::LINES_POINTS);
            
            // 计算每个距离区间的丢包率
            std::map<int, std::pair<int, int>> distanceBins; // 距离区间 -> (接收数, 发送数)
            
            // 首先统计每个区间的发送总数
            for (const auto& entry : m_senderPositions) {
                Ptr<Node> receiver = NodeList::GetNode(1); // 假设节点1是接收方
                Ptr<MobilityModel> receiverMob = receiver->GetObject<MobilityModel>();
                if (receiverMob) {
                    Vector senderPos = entry.second;
                    Vector receiverPos = receiverMob->GetPosition();
                    double distance = CalculateDistance(senderPos, receiverPos);
                    int bin = static_cast<int>(distance);
                    
                    distanceBins[bin].second++; // 发送数加1
                }
            }
            
            // 然后统计每个区间的接收数
            for (const auto& entry : m_distanceVsLoss) {
                int bin = static_cast<int>(entry.first);
                distanceBins[bin].first += entry.second; // 接收数加1
            }
            
            // 计算丢包率并添加到图表
            for (const auto& bin : distanceBins) {
                if (bin.second.second > 0) {
                    double lossRate = 1.0 - static_cast<double>(bin.second.first) / bin.second.second;
                    dataset.Add(bin.first, lossRate);
                }
            }
            
            plot.AddDataset(dataset);
            std::ofstream lossFile(m_outputDir + "/loss_vs_distance_" + weatherName + "_" + trajectoryName + ".plt");
            plot.GenerateOutput(lossFile);
        }
    }
    
    // 输出性能统计结果
    void PrintStatistics() {
        double simulationDuration = (Simulator::Now() - m_firstPacketTime).GetSeconds();
        if (simulationDuration <= 0) simulationDuration = 1e-9; // 避免除零
        
        // 计算丢包率
        double packetLossRate = 0.0;
        if (m_totalPacketsSent > 0) {
            packetLossRate = 1.0 - (double)m_totalPacketsReceived / m_totalPacketsSent;
        }
        
        // 计算平均时延
        double avgDelay = 0.0;
        if (!m_delayValues.empty()) {
            for (double d : m_delayValues) {
                avgDelay += d;
            }
            avgDelay /= m_delayValues.size();
        }
        
        // 计算带宽 (bps)
        double bandwidth = (m_totalBytesReceived * 8.0) / simulationDuration;
        
        // 输出结果
        NS_LOG_INFO("=== 性能统计结果 ===");
        NS_LOG_INFO("总发送数据包: " << m_totalPacketsSent);
        NS_LOG_INFO("总接收数据包: " << m_totalPacketsReceived);
        NS_LOG_INFO("丢包率: " << packetLossRate * 100 << "%");
        NS_LOG_INFO("平均时延: " << avgDelay << " 秒");
        NS_LOG_INFO("平均带宽: " << bandwidth << " bps");
        
        // 输出到文件
        std::ofstream outFile(m_outputDir + "/simulation_summary.txt");
        if (outFile.is_open()) {
            outFile << "=== 仿真总结 ===" << std::endl;
            outFile << "仿真时间: " << Simulator::Now().GetSeconds() << " 秒" << std::endl;
            outFile << "总发送数据包: " << m_totalPacketsSent << std::endl;
            outFile << "总接收数据包: " << m_totalPacketsReceived << std::endl;
            outFile << "丢包率: " << packetLossRate * 100 << "%" << std::endl;
            outFile << "平均时延: " << avgDelay << " 秒" << std::endl;
            outFile << "平均带宽: " << bandwidth << " bps" << std::endl;
            outFile.close();
        }
    }
    
    // 设置输出目录
    void SetOutputDirectory(std::string dir) {
        m_outputDir = dir;
        std::string cmd = "mkdir -p " + m_outputDir;
        int ret = system(cmd.c_str());
	(void)ret; // 显式忽略返回值，同时避免编译器警告
    }
    
private:
    uint32_t m_totalPacketsSent;
    uint32_t m_totalPacketsReceived;
    uint32_t m_totalBytesReceived;
    Time m_firstPacketTime;
    std::vector<double> m_delayValues;
    std::map<uint32_t, Time> m_sendTimes; // 数据包UID到发送时间的映射
    std::map<uint32_t, Vector> m_senderPositions; // 数据包UID到发送位置的映射
    std::map<double, double> m_distanceVsDelay; // 距离到时延的映射
    std::map<double, int> m_distanceVsLoss; // 距离到接收数的映射
    std::string m_outputDir; // 结果输出目录
    Ptr<UniformRandomVariable> m_rand = CreateObject<UniformRandomVariable>();
};

// 配置移动模型
void ConfigureMobility(NodeContainer& nodes, TrajectoryMode mode, double height1, double height2) {
    MobilityHelper mobility;
    
    switch (mode) {
        case RANDOM_WALK: {
            // 随机游走模型
            mobility.SetPositionAllocator("ns3::RandomBoxPositionAllocator",
                "X", StringValue("ns3::UniformRandomVariable[Min=0.0|Max=" + std::to_string(SCENE_WIDTH) + "]"),
                "Y", StringValue("ns3::UniformRandomVariable[Min=0.0|Max=" + std::to_string(SCENE_HEIGHT) + "]"),
                "Z", StringValue("ns3::ConstantRandomVariable[Constant=" + std::to_string(height1) + "]"));
            
            mobility.SetMobilityModel("ns3::RandomWalk2dMobilityModel",
                "Bounds", RectangleValue(Rectangle(0, SCENE_WIDTH, 0, SCENE_HEIGHT)),
                "Speed", StringValue("ns3::UniformRandomVariable[Min=1.0|Max=5.0]"));
            mobility.Install(nodes.Get(0));
            
            // 第二个节点使用不同高度
            mobility.SetPositionAllocator("ns3::RandomBoxPositionAllocator",
                "Z", StringValue("ns3::ConstantRandomVariable[Constant=" + std::to_string(height2) + "]"));
            mobility.Install(nodes.Get(1));
            break;
        }
        
        case LINEAR_PATROL: {
            // 线性巡逻模型
            Ptr<ListPositionAllocator> positionAlloc = CreateObject<ListPositionAllocator>();
            positionAlloc->Add(Vector(0.0, SCENE_HEIGHT/2, height1));
            positionAlloc->Add(Vector(SCENE_WIDTH, SCENE_HEIGHT/2, height2));
            mobility.SetPositionAllocator(positionAlloc);
            
            mobility.SetMobilityModel("ns3::WaypointMobilityModel");
            mobility.Install(nodes);
            
            // 配置第一个节点的路径点
            Ptr<WaypointMobilityModel> waypoint1 = nodes.Get(0)->GetObject<WaypointMobilityModel>();
            waypoint1->AddWaypoint(Waypoint(Seconds(0), Vector(0.0, SCENE_HEIGHT/2, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(SIMULATION_TIME/2), Vector(SCENE_WIDTH, SCENE_HEIGHT/2, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(SIMULATION_TIME), Vector(0.0, SCENE_HEIGHT/2, height1)));
            
            // 配置第二个节点的路径点
            Ptr<WaypointMobilityModel> waypoint2 = nodes.Get(1)->GetObject<WaypointMobilityModel>();
            waypoint2->AddWaypoint(Waypoint(Seconds(0), Vector(SCENE_WIDTH, SCENE_HEIGHT/2, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(SIMULATION_TIME/2), Vector(0.0, SCENE_HEIGHT/2, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(SIMULATION_TIME), Vector(SCENE_WIDTH, SCENE_HEIGHT/2, height2)));
            break;
        }
        
        case CIRCULAR_PATROL: {
            // 圆形巡逻模型
            mobility.SetPositionAllocator("ns3::RandomBoxPositionAllocator",
                "X", StringValue("ns3::ConstantRandomVariable[Constant=" + std::to_string(SCENE_WIDTH/2) + "]"),
                "Y", StringValue("ns3::ConstantRandomVariable[Constant=" + std::to_string(SCENE_HEIGHT/2) + "]"),
                "Z", StringValue("ns3::ConstantRandomVariable[Constant=" + std::to_string(height1) + "]"));
            
            mobility.SetMobilityModel("ns3::CircularMobilityModel",
                "Center", VectorValue(Vector(SCENE_WIDTH/2, SCENE_HEIGHT/2, height1)),
                "Radius", DoubleValue(SCENE_WIDTH/4),
                "Speed", DoubleValue(2.0),
                "Direction", DoubleValue(0),
                "Distance", DoubleValue(0));
            mobility.Install(nodes.Get(0));
            
            // 第二个节点反向环绕
            mobility.SetMobilityModel("ns3::CircularMobilityModel",
                "Center", VectorValue(Vector(SCENE_WIDTH/2, SCENE_HEIGHT/2, height2)),
                "Radius", DoubleValue(SCENE_WIDTH/4),
                "Speed", DoubleValue(2.0),
                "Direction", DoubleValue(M_PI),
                "Distance", DoubleValue(0));
            mobility.Install(nodes.Get(1));
            break;
        }
        
        case WAYPOINT: {
            // 自定义路径点模型
            Ptr<ListPositionAllocator> positionAlloc = CreateObject<ListPositionAllocator>();
            positionAlloc->Add(Vector(10.0, 10.0, height1));
            positionAlloc->Add(Vector(SCENE_WIDTH-10, 10.0, height2));
            mobility.SetPositionAllocator(positionAlloc);
            
            mobility.SetMobilityModel("ns3::WaypointMobilityModel");
            mobility.Install(nodes);
            
            // 第一个节点的路径点
            Ptr<WaypointMobilityModel> waypoint1 = nodes.Get(0)->GetObject<WaypointMobilityModel>();
            waypoint1->AddWaypoint(Waypoint(Seconds(0), Vector(10.0, 10.0, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(20), Vector(SCENE_WIDTH-10, 10.0, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(40), Vector(SCENE_WIDTH-10, SCENE_HEIGHT-10, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(60), Vector(10.0, SCENE_HEIGHT-10, height1)));
            waypoint1->AddWaypoint(Waypoint(Seconds(80), Vector(10.0, 10.0, height1)));
            
            // 第二个节点的路径点
            Ptr<WaypointMobilityModel> waypoint2 = nodes.Get(1)->GetObject<WaypointMobilityModel>();
            waypoint2->AddWaypoint(Waypoint(Seconds(0), Vector(SCENE_WIDTH-10, 10.0, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(20), Vector(SCENE_WIDTH-10, SCENE_HEIGHT-10, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(40), Vector(10.0, SCENE_HEIGHT-10, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(60), Vector(10.0, 10.0, height2)));
            waypoint2->AddWaypoint(Waypoint(Seconds(80), Vector(SCENE_WIDTH-10, 10.0, height2)));
            break;
        }
    }
}

// 生成多组实验对比图表
void GenerateComparisonPlots() {
    std::string outputDir = "results/comparison";
    std::string cmd = "mkdir -p " + outputDir;
    int ret = system(cmd.c_str());
    (void)ret; // 显式忽略返回值，同时避免编译器警告
    
    // 1. 不同天气条件下的性能对比
    {
        Gnuplot plot(outputDir + "/weather_comparison.png");
        plot.SetTitle("Performance Comparison Under Different Weather Conditions");
        plot.SetTerminal("pngcairo enhanced font 'Arial,10'");
        plot.SetLegend("Weather Condition", "Value");
        
        // 收集不同天气的统计数据
        std::map<std::string, std::vector<double>> weatherData;
        weatherData["sunny"].resize(3); // [丢包率, 平均时延, 带宽]
        weatherData["rainy"].resize(3);
        weatherData["foggy"].resize(3);
        
        // 从实验结果文件中读取数据（这里使用示例数据）
        weatherData["sunny"] = {0.12, 0.0025, 45000000};
        weatherData["rainy"] = {0.35, 0.0042, 32000000};
        weatherData["foggy"] = {0.58, 0.0068, 22000000};
        
        // 丢包率数据集
        Gnuplot2dDataset lossDataset;
        lossDataset.SetTitle("Packet Loss Rate");
        lossDataset.SetStyle(Gnuplot2dDataset::Style::LINES_POINTS);
        
        // 时延数据集
        Gnuplot2dDataset delayDataset;
        delayDataset.SetTitle("Average Delay (s)");
        delayDataset.SetStyle(Gnuplot2dDataset::Style::LINES_POINTS);
        
        // 带宽数据集
        Gnuplot2dDataset bandwidthDataset;
        bandwidthDataset.SetTitle("Bandwidth (bps)");
        bandwidthDataset.SetStyle(Gnuplot2dDataset::Style::LINES_POINTS);
        
        int i = 1;
        for (const auto& entry : weatherData) {
            lossDataset.Add(i, entry.second[0]);
            delayDataset.Add(i, entry.second[1]);
            bandwidthDataset.Add(i, entry.second[2]);
            i++;
        }
        
        // 设置x轴标签
        plot.AppendExtra("set xtics (\"Sunny\" 1, \"Rainy\" 2, \"Foggy\" 3)");
        
        plot.AddDataset(lossDataset);
        plot.AddDataset(delayDataset);
        plot.AddDataset(bandwidthDataset);
        std::ofstream weatherFile(outputDir + "/weather_comparison.plt");
        plot.GenerateOutput(weatherFile);
    }
}

// 运行多参数对比实验
void RunParameterSweep() {
    // 要测试的参数组合
    std::vector<WeatherCondition> weathers = {SUNNY, RAINY, FOGGY};
    std::vector<TrajectoryMode> trajectories = {RANDOM_WALK, LINEAR_PATROL, CIRCULAR_PATROL};
    std::vector<double> heights = {10.0, 30.0, 50.0}; // 无人机高度
    
    int experimentId = 0;
    
    for (auto weather : weathers) {
        for (auto trajectory : trajectories) {
            for (double height : heights) {
                NS_LOG_INFO("运行实验 " << experimentId << " - 天气: " << weather 
                                      << ", 轨迹: " << trajectory << ", 高度: " << height);
                
                // 重置障碍物列表
                trees.clear();
                buildings.clear();
                GenerateObstacles();
                
                // 创建节点
                NodeContainer drones;
                drones.Create(2);
                
                // 配置移动模型（两个无人机使用相同高度）
                ConfigureMobility(drones, trajectory, height, height);
                
                // 配置WiFi通信
                WifiHelper wifi;
                wifi.SetStandard(WIFI_STANDARD_80211ac);
                
                YansWifiPhyHelper wifiPhy;
                wifiPhy.Set("TxPowerStart", DoubleValue(30.0)); // 增加发射功率
                wifiPhy.Set("TxPowerEnd", DoubleValue(30.0));
                wifiPhy.Set("RxSensitivity", DoubleValue(-95.0)); // 降低接收灵敏度阈值

                YansWifiChannelHelper wifiChannel = YansWifiChannelHelper::Default();

                // 简化传播损耗模型，只使用基本的距离衰减
                wifiChannel.AddPropagationLoss("ns3::LogDistancePropagationLossModel",
                                              "Exponent", DoubleValue(2.0),
                                              "ReferenceDistance", DoubleValue(1.0),
                                              "ReferenceLoss", DoubleValue(40.0));
                
                // 根据天气条件调整传播指数
                switch (weather) {
                    case RAINY:
                        Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::LogDistancePropagationLossModel/Exponent", DoubleValue(2.5));
                        break;
                    case FOGGY:
                        Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::LogDistancePropagationLossModel/Exponent", DoubleValue(3.0));
                        break;
                    default: // SUNNY
                        Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::LogDistancePropagationLossModel/Exponent", DoubleValue(2.0));
                        break;
                }
                
                wifiChannel.SetPropagationDelay("ns3::ConstantSpeedPropagationDelayModel");
                wifiPhy.SetChannel(wifiChannel.Create());

                
                // 配置MAC层和速率控制
                WifiMacHelper wifiMac;
                wifiMac.SetType("ns3::AdhocWifiMac");
                wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                            "DataMode", StringValue("VhtMcs9"),
                                            "ControlMode", StringValue("VhtMcs0"));
                
                NetDeviceContainer devices = wifi.Install(wifiPhy, wifiMac, drones);
                
                // 安装互联网协议栈
                InternetStackHelper stack;
                stack.Install(drones);
                
                Ipv4AddressHelper address;
                address.SetBase("10.0.0.0", "*************");
                Ipv4InterfaceContainer interfaces = address.Assign(devices);
                
                // 配置应用
                uint16_t port = 9;
                PacketSinkHelper sink("ns3::UdpSocketFactory",
                                      InetSocketAddress(Ipv4Address::GetAny(), port));
                ApplicationContainer sinkApps = sink.Install(drones.Get(1));
                sinkApps.Start(Seconds(1.0));
                sinkApps.Stop(Seconds(SIMULATION_TIME));
                
                OnOffHelper onoff("ns3::UdpSocketFactory",
                                  InetSocketAddress(interfaces.GetAddress(1), port));
                onoff.SetAttribute("PacketSize", UintegerValue(PACKET_SIZE));
                onoff.SetAttribute("OnTime", StringValue("ns3::ConstantRandomVariable[Constant=1]"));
                onoff.SetAttribute("OffTime", StringValue("ns3::ConstantRandomVariable[Constant=0]"));
                onoff.SetAttribute("DataRate", DataRateValue(DataRate(std::to_string((uint64_t)DATA_RATE) + "Mbps")));
                
                ApplicationContainer sourceApps = onoff.Install(drones.Get(0));
                sourceApps.Start(Seconds(2.0));
                sourceApps.Stop(Seconds(SIMULATION_TIME - 1.0));
                
                // 配置性能监控
                std::string outputDir = "results/exp_" + std::to_string(experimentId);
                Ptr<PerformanceMonitor> monitor = CreateObject<PerformanceMonitor>(outputDir);
                monitor->SetOutputDirectory(outputDir);
                
                // 连接跟踪源
                Config::ConnectWithoutContext("/NodeList/0/ApplicationList/0/$ns3::OnOffApplication/Tx",
                                             MakeCallback(&PerformanceMonitor::PacketSent, monitor));
                Config::ConnectWithoutContext("/NodeList/1/ApplicationList/0/$ns3::PacketSink/Rx",
                                             MakeCallback(&PerformanceMonitor::PacketReceived, monitor));
                
                // 开始记录无人机位置
                monitor->LogDronePositions(drones.Get(0), 0);
                monitor->LogDronePositions(drones.Get(1), 1);
                
                // 调度结果输出
                Simulator::Schedule(Seconds(SIMULATION_TIME - 0.1), 
                                   &PerformanceMonitor::PrintStatistics, monitor);
                Simulator::Schedule(Seconds(SIMULATION_TIME - 0.1), 
                                   &PerformanceMonitor::GeneratePlots, monitor, weather, trajectory);
                
                // 运行仿真
                Simulator::Stop(Seconds(SIMULATION_TIME));
                Simulator::Run();
                Simulator::Destroy();
                
                experimentId++;
            }
        }
    }
    
    // 生成参数对比汇总图表
    GenerateComparisonPlots();
}

int main (int argc, char *argv[])
{
    // 命令行参数
    int weatherInt = 0;         // 用整数接收天气参数
    int trajectoryInt = 0;      // 用整数接收轨迹参数
    double distanceStep = 0.5;
    uint32_t totalPackets = 1000;
    bool enableLogging = false;
    bool runSweep = false;
    double droneHeight = 30.0;
    std::string outputDir = "results";
    
    CommandLine cmd;
    cmd.AddValue("weather", "Weather condition (0=SUNNY, 1=RAINY, 2=FOGGY)", weatherInt);
    cmd.AddValue("trajectory", "Trajectory mode (0-3)", trajectoryInt);
    cmd.AddValue("distanceStep", "Distance step between drones (meters)", distanceStep);
    cmd.AddValue("totalPackets", "Total number of packets to send", totalPackets);
    cmd.AddValue("enableLogging", "Enable detailed logging", enableLogging);
    cmd.AddValue("runSweep", "Run parameter sweep experiment", runSweep);
    cmd.AddValue("droneHeight", "Drone height (meters)", droneHeight);
    cmd.AddValue("outputDir", "Directory for output results", outputDir);
    cmd.Parse(argc, argv);

    // 转换为枚举
    WeatherCondition weather = IntToWeather(weatherInt);
    TrajectoryMode trajectory = IntToTrajectory(trajectoryInt);
    
    // 配置日志
    if (enableLogging) {
        LogComponentEnable ("DroneCommunicationSimulation", LOG_LEVEL_INFO);
        LogComponentEnable ("OnOffApplication", LOG_LEVEL_INFO);
        LogComponentEnable ("PacketSink", LOG_LEVEL_INFO);
    }
    
    // 如果需要运行多参数实验
    if (runSweep) {
        NS_LOG_INFO ("Starting parameter sweep experiment...");
        RunParameterSweep();
        return 0;
    }
    
    NS_LOG_INFO ("Starting drone communication simulation...");
    NS_LOG_INFO ("Weather condition: " << weather);
    NS_LOG_INFO ("Trajectory mode: " << trajectory);
    
    // 生成障碍物
    GenerateObstacles();
    
    // 1. 创建节点（两个无人机）
    NodeContainer drones;
    drones.Create (2);
    NS_LOG_INFO ("Created " << drones.GetN () << " drone nodes");
    
    // 2. 配置移动模型
    ConfigureMobility(drones, trajectory, droneHeight, droneHeight + 5.0); // 两个无人机高度略有差异
    
    // 3. 配置WiFi通信 - IEEE 802.11ac
    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211ac);
    
    // 配置物理层
    YansWifiPhyHelper wifiPhy;
    wifiPhy.Set("TxPowerStart", DoubleValue(30.0)); // 增加发射功率
    wifiPhy.Set("TxPowerEnd", DoubleValue(30.0));
    wifiPhy.Set("RxSensitivity", DoubleValue(-95.0)); // 降低接收灵敏度阈值

    YansWifiChannelHelper wifiChannel = YansWifiChannelHelper::Default();

    // 简化传播损耗模型，只使用基本的距离衰减
    wifiChannel.AddPropagationLoss("ns3::LogDistancePropagationLossModel",
                                  "Exponent", DoubleValue(2.0),
                                  "ReferenceDistance", DoubleValue(1.0),
                                  "ReferenceLoss", DoubleValue(40.0));
    
    // 根据天气条件调整传播损耗
    switch (weather) {
        case RAINY:
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/PathLossExponent", DoubleValue(8.5));
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/ShadowingSigma", DoubleValue(8.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallTreeLoss", DoubleValue(8.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeTreeLoss", DoubleValue(18.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallBuildingLoss", DoubleValue(25.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeBuildingLoss", DoubleValue(45.0));
            break;
        case FOGGY:
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/PathLossExponent", DoubleValue(9.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/ShadowingSigma", DoubleValue(10.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallTreeLoss", DoubleValue(10.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeTreeLoss", DoubleValue(20.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallBuildingLoss", DoubleValue(30.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeBuildingLoss", DoubleValue(50.0));
            break;
        default: // SUNNY
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/PathLossExponent", DoubleValue(7.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/2/$ns3::LogDistancePropagationLossModel/ShadowingSigma", DoubleValue(4.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallTreeLoss", DoubleValue(5.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeTreeLoss", DoubleValue(15.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/SmallBuildingLoss", DoubleValue(20.0));
            Config::Set("/ChannelList/0/PropagationLossModelList/0/$ns3::ObstacleShadowingLossModel/LargeBuildingLoss", DoubleValue(40.0));
            break;
    }
    
    wifiChannel.SetPropagationDelay("ns3::ConstantSpeedPropagationDelayModel");
    wifiPhy.SetChannel(wifiChannel.Create());
    
    // 配置MAC层
    WifiMacHelper wifiMac;
    wifiMac.SetType("ns3::AdhocWifiMac");
    
    // 配置速率控制算法
    wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                "DataMode", StringValue("VhtMcs9"),
                                "ControlMode", StringValue("VhtMcs0"));
    
    // 安装WiFi设备
    NetDeviceContainer devices = wifi.Install(wifiPhy, wifiMac, drones);
    
    // 4. 安装互联网协议栈
    InternetStackHelper stack;
    stack.Install(drones);
    
    // 分配IP地址
    Ipv4AddressHelper address;
    address.SetBase("10.0.0.0", "*************");
    Ipv4InterfaceContainer interfaces = address.Assign(devices);
    
    NS_LOG_INFO("Assigned IP addresses: " << interfaces.GetAddress(0) << " and " << interfaces.GetAddress(1));
    
    // 5. 配置应用层 - 数据发送与接收
    uint16_t port = 9;
    
    // 接收方应用
    PacketSinkHelper sink("ns3::UdpSocketFactory",
                          InetSocketAddress(Ipv4Address::GetAny(), port));
    ApplicationContainer sinkApps = sink.Install(drones.Get(1));
    sinkApps.Start(Seconds(1.0));
    sinkApps.Stop(Seconds(SIMULATION_TIME));
    
    // 发送方应用
    OnOffHelper onoff("ns3::UdpSocketFactory",
                      InetSocketAddress(interfaces.GetAddress(1), port));
    onoff.SetAttribute("PacketSize", UintegerValue(PACKET_SIZE));
    onoff.SetAttribute("OnTime", StringValue("ns3::ConstantRandomVariable[Constant=1]"));
    onoff.SetAttribute("OffTime", StringValue("ns3::ConstantRandomVariable[Constant=0]"));
    onoff.SetAttribute("DataRate", DataRateValue(DataRate(std::to_string((uint64_t)DATA_RATE) + "Mbps")));
    
    ApplicationContainer sourceApps = onoff.Install(drones.Get(0));
    sourceApps.Start(Seconds(2.0));
    sourceApps.Stop(Seconds(SIMULATION_TIME - 1.0));
    
    // 6. 配置性能监控
    Ptr<PerformanceMonitor> monitor = CreateObject<PerformanceMonitor>(outputDir);
    monitor->SetOutputDirectory(outputDir);
    
    // 连接发送跟踪源
// OnOffApplication的Tx跟踪源只传递Packet，因此回调函数也应只接受Packet
Config::ConnectWithoutContext("/NodeList/0/ApplicationList/0/$ns3::OnOffApplication/Tx",
                             MakeCallback(&PerformanceMonitor::PacketSent, monitor));
    
    // 连接接收跟踪源
    Config::ConnectWithoutContext("/NodeList/1/ApplicationList/0/$ns3::PacketSink/Rx",
                                 MakeCallback(&PerformanceMonitor::PacketReceived, monitor));
    
    // 开始记录无人机位置
    monitor->LogDronePositions(drones.Get(0), 0);
    monitor->LogDronePositions(drones.Get(1), 1);
    
    // 7. 在仿真结束前输出统计结果和图表
    Simulator::Schedule(Seconds(SIMULATION_TIME - 0.1), &PerformanceMonitor::PrintStatistics, monitor);
    Simulator::Schedule(Seconds(SIMULATION_TIME - 0.1), 
                       &PerformanceMonitor::GeneratePlots, monitor, weather, trajectory);
    
    // 启动仿真
    Simulator::Stop(Seconds(SIMULATION_TIME));
    NS_LOG_INFO("Starting simulation for " << SIMULATION_TIME << " seconds...");
    Simulator::Run();
    Simulator::Destroy();
    
    return 0;
}
